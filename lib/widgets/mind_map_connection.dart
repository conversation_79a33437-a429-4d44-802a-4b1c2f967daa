import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Widget for drawing connections between mind map nodes
class MindMapConnection extends StatelessWidget {
  final Offset startPosition;
  final Offset endPosition;
  final Color color;
  final double width;

  const MindMapConnection({
    super.key,
    required this.startPosition,
    required this.endPosition,
    required this.color,
    this.width = 2.0,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: ConnectionPainter(
        startPosition: startPosition,
        endPosition: endPosition,
        color: color,
        width: width,
      ),
      size: const Size(2000, 2000),
    );
  }
}

/// Custom painter for drawing curved connections between nodes
class ConnectionPainter extends CustomPainter {
  final Offset startPosition;
  final Offset endPosition;
  final Color color;
  final double width;

  ConnectionPainter({
    required this.startPosition,
    required this.endPosition,
    required this.color,
    required this.width,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = width
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Calculate control points for a smooth curve
    final dx = endPosition.dx - startPosition.dx;
    final dy = endPosition.dy - startPosition.dy;
    final distance = math.sqrt(dx * dx + dy * dy);
    
    // Create a curved path
    final path = Path();
    path.moveTo(startPosition.dx, startPosition.dy);
    
    if (distance > 0) {
      // Calculate control points for a smooth curve
      final controlPoint1 = Offset(
        startPosition.dx + dx * 0.3,
        startPosition.dy,
      );
      final controlPoint2 = Offset(
        endPosition.dx - dx * 0.3,
        endPosition.dy,
      );
      
      path.cubicTo(
        controlPoint1.dx,
        controlPoint1.dy,
        controlPoint2.dx,
        controlPoint2.dy,
        endPosition.dx,
        endPosition.dy,
      );
    } else {
      path.lineTo(endPosition.dx, endPosition.dy);
    }

    canvas.drawPath(path, paint);

    // Draw arrow at the end
    _drawArrow(canvas, paint);
  }

  void _drawArrow(Canvas canvas, Paint paint) {
    final dx = endPosition.dx - startPosition.dx;
    final dy = endPosition.dy - startPosition.dy;
    final distance = math.sqrt(dx * dx + dy * dy);
    
    if (distance == 0) return;

    final angle = math.atan2(dy, dx);
    const arrowLength = 8.0;
    const arrowAngle = math.pi / 6; // 30 degrees

    final arrowPoint1 = Offset(
      endPosition.dx - arrowLength * math.cos(angle - arrowAngle),
      endPosition.dy - arrowLength * math.sin(angle - arrowAngle),
    );

    final arrowPoint2 = Offset(
      endPosition.dx - arrowLength * math.cos(angle + arrowAngle),
      endPosition.dy - arrowLength * math.sin(angle + arrowAngle),
    );

    final arrowPath = Path()
      ..moveTo(endPosition.dx, endPosition.dy)
      ..lineTo(arrowPoint1.dx, arrowPoint1.dy)
      ..moveTo(endPosition.dx, endPosition.dy)
      ..lineTo(arrowPoint2.dx, arrowPoint2.dy);

    canvas.drawPath(arrowPath, paint);
  }

  @override
  bool shouldRepaint(ConnectionPainter oldDelegate) {
    return startPosition != oldDelegate.startPosition ||
           endPosition != oldDelegate.endPosition ||
           color != oldDelegate.color ||
           width != oldDelegate.width;
  }
}
