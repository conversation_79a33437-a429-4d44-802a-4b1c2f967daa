import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/mind_map_provider.dart';
import '../models/mind_map_node.dart';
import 'mind_map_node_widget.dart';
import 'mind_map_connection.dart';
import 'node_editor_dialog.dart';

/// Canvas widget for displaying and interacting with mind map
class MindMapCanvas extends StatefulWidget {
  const MindMapCanvas({super.key});

  @override
  State<MindMapCanvas> createState() => _MindMapCanvasState();
}

class _MindMapCanvasState extends State<MindMapCanvas> {
  final TransformationController _transformationController = TransformationController();

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MindMapProvider>(
      builder: (context, provider, child) {
        final mindMap = provider.currentMindMap;
        if (mindMap == null) {
          return const Center(
            child: Text('No mind map to display'),
          );
        }

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                mindMap.theme.backgroundColor,
                mindMap.theme.backgroundColor.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: InteractiveViewer(
            transformationController: _transformationController,
            boundaryMargin: const EdgeInsets.all(100),
            minScale: 0.1,
            maxScale: 3.0,
            child: SizedBox(
              width: 2000,
              height: 2000,
              child: Stack(
                children: [
                  // Draw connections first (behind nodes)
                  ..._buildConnections(mindMap, provider),
                  // Draw nodes on top
                  ..._buildNodes(mindMap, provider),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildConnections(dynamic mindMap, MindMapProvider provider) {
    final connections = <Widget>[];
    
    for (final node in mindMap.nodes.values) {
      if (node.parentId != null) {
        final parent = mindMap.nodes[node.parentId];
        if (parent != null) {
          connections.add(
            MindMapConnection(
              key: Key('connection_${parent.id}_${node.id}'),
              startPosition: _getNodeCenter(parent),
              endPosition: _getNodeCenter(node),
              color: mindMap.theme.connectionColor,
              width: mindMap.theme.connectionWidth,
            ),
          );
        }
      }
    }
    
    return connections;
  }

  List<Widget> _buildNodes(dynamic mindMap, MindMapProvider provider) {
    final nodes = <Widget>[];
    
    for (final node in mindMap.nodes.values) {
      nodes.add(
        Positioned(
          left: node.position.dx + 1000 - node.size.width / 2,
          top: node.position.dy + 1000 - node.size.height / 2,
          child: MindMapNodeWidget(
            key: Key('node_${node.id}'),
            node: node,
            isSelected: provider.selectedNodeId == node.id,
            isEditMode: provider.isEditMode,
            onTap: () => _handleNodeTap(node, provider),
            onDoubleTap: () => _handleNodeDoubleTap(node, provider),
            onDragUpdate: (details) => _handleNodeDrag(node, details, provider),
          ),
        ),
      );
    }
    
    return nodes;
  }

  Offset _getNodeCenter(MindMapNode node) {
    return Offset(
      node.position.dx + 1000,
      node.position.dy + 1000,
    );
  }

  void _handleNodeTap(MindMapNode node, MindMapProvider provider) {
    if (provider.isEditMode) {
      provider.selectNode(
        provider.selectedNodeId == node.id ? null : node.id,
      );
    } else {
      // In view mode, maybe show node details
      _showNodeDetails(node);
    }
  }

  Future<void> _handleNodeDoubleTap(MindMapNode node, MindMapProvider provider) async {
    if (!provider.isEditMode) return;

    final result = await showDialog<MindMapNode>(
      context: context,
      builder: (context) => NodeEditorDialog(node: node),
    );

    if (result != null) {
      provider.updateNode(node.id, result);
    }
  }

  void _handleNodeDrag(
    MindMapNode node,
    DragUpdateDetails details,
    MindMapProvider provider,
  ) {
    if (!provider.isEditMode) return;

    final newPosition = Offset(
      node.position.dx + details.delta.dx,
      node.position.dy + details.delta.dy,
    );

    final updatedNode = node.copyWith(position: newPosition);
    provider.updateNode(node.id, updatedNode);
  }

  void _showNodeDetails(MindMapNode node) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(node.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (node.subtitle != null) ...[
              Text(
                'Subtitle:',
                style: Theme.of(context).textTheme.labelMedium,
              ),
              Text(node.subtitle!),
              const SizedBox(height: 8),
            ],
            if (node.description != null) ...[
              Text(
                'Description:',
                style: Theme.of(context).textTheme.labelMedium,
              ),
              Text(node.description!),
              const SizedBox(height: 8),
            ],
            Text(
              'Level: ${node.level}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              'Created: ${_formatDate(node.createdAt)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
