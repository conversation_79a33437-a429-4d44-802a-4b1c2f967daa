import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../models/mind_map_node.dart';

/// Dialog for editing or creating mind map nodes
class NodeEditorDialog extends StatefulWidget {
  final MindMapNode? node;
  final String? parentId;

  const NodeEditorDialog({
    super.key,
    this.node,
    this.parentId,
  });

  @override
  State<NodeEditorDialog> createState() => _NodeEditorDialogState();
}

class _NodeEditorDialogState extends State<NodeEditorDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _subtitleController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  Color _selectedColor = Colors.blue;
  IconData? _selectedIcon;
  
  final List<IconData> _availableIcons = [
    Icons.lightbulb,
    Icons.star,
    Icons.favorite,
    Icons.work,
    Icons.school,
    Icons.home,
    Icons.science,
    Icons.psychology,
    Icons.analytics,
    Icons.business,
    Icons.article,
    Icons.library_books,
    Icons.assignment,
    Icons.task_alt,
    Icons.flag,
    Icons.bookmark,
  ];

  @override
  void initState() {
    super.initState();
    if (widget.node != null) {
      _titleController.text = widget.node!.title;
      _subtitleController.text = widget.node!.subtitle ?? '';
      _descriptionController.text = widget.node!.description ?? '';
      _selectedColor = widget.node!.color;
      _selectedIcon = widget.node!.icon;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _subtitleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTextFields(),
                      const SizedBox(height: 24),
                      _buildColorPicker(),
                      const SizedBox(height: 24),
                      _buildIconPicker(),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          widget.node != null ? Icons.edit : Icons.add,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Text(
          widget.node != null ? 'تحرير العقدة' : 'إضافة عقدة جديدة',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.pop(context),
        ),
      ],
    );
  }

  Widget _buildTextFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _titleController,
          decoration: const InputDecoration(
            labelText: 'العنوان *',
            hintText: 'أدخل عنوان العقدة',
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'العنوان مطلوب';
            }
            return null;
          },
          maxLength: 50,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _subtitleController,
          decoration: const InputDecoration(
            labelText: 'العنوان الفرعي',
            hintText: 'أدخل عنوان فرعي (اختياري)',
          ),
          maxLength: 100,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'الوصف',
            hintText: 'أدخل وصف مفصل (اختياري)',
          ),
          maxLines: 3,
          maxLength: 500,
        ),
      ],
    );
  }

  Widget _buildColorPicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'اللون',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _showColorPicker,
          child: Container(
            width: double.infinity,
            height: 50,
            decoration: BoxDecoration(
              color: _selectedColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
            ),
            child: Center(
              child: Text(
                'اضغط لاختيار اللون',
                style: TextStyle(
                  color: _selectedColor.computeLuminance() > 0.5 
                      ? Colors.black 
                      : Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildIconPicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأيقونة',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              // No icon option
              GestureDetector(
                onTap: () => setState(() => _selectedIcon = null),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: _selectedIcon == null
                          ? Theme.of(context).colorScheme.primary
                          : Colors.grey.withValues(alpha: 0.3),
                      width: _selectedIcon == null ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.close, size: 20),
                ),
              ),
              // Available icons
              ..._availableIcons.map((icon) => GestureDetector(
                onTap: () => setState(() => _selectedIcon = icon),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _selectedIcon == icon
                        ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                        : null,
                    border: Border.all(
                      color: _selectedIcon == icon
                          ? Theme.of(context).colorScheme.primary
                          : Colors.grey.withValues(alpha: 0.3),
                      width: _selectedIcon == icon ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    size: 20,
                    color: _selectedIcon == icon 
                        ? Theme.of(context).colorScheme.primary 
                        : null,
                  ),
                ),
              )),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed: _saveNode,
          child: Text(widget.node != null ? 'حفظ' : 'إضافة'),
        ),
      ],
    );
  }

  void _showColorPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللون'),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: _selectedColor,
            onColorChanged: (color) => setState(() => _selectedColor = color),
            enableAlpha: false,
            displayThumbColor: true,
            pickerAreaHeightPercent: 0.8,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('تم'),
          ),
        ],
      ),
    );
  }

  void _saveNode() {
    if (!_formKey.currentState!.validate()) return;

    final node = widget.node?.copyWith(
      title: _titleController.text.trim(),
      subtitle: _subtitleController.text.trim().isEmpty 
          ? null 
          : _subtitleController.text.trim(),
      description: _descriptionController.text.trim().isEmpty 
          ? null 
          : _descriptionController.text.trim(),
      color: _selectedColor,
      icon: _selectedIcon,
    ) ?? MindMapNode(
      title: _titleController.text.trim(),
      subtitle: _subtitleController.text.trim().isEmpty 
          ? null 
          : _subtitleController.text.trim(),
      description: _descriptionController.text.trim().isEmpty 
          ? null 
          : _descriptionController.text.trim(),
      color: _selectedColor,
      icon: _selectedIcon,
      parentId: widget.parentId,
    );

    Navigator.pop(context, node);
  }
}
