import 'package:flutter/material.dart';
import '../models/mind_map_node.dart';

/// Widget for displaying individual mind map nodes
class MindMapNodeWidget extends StatelessWidget {
  final MindMapNode node;
  final bool isSelected;
  final bool isEditMode;
  final VoidCallback onTap;
  final VoidCallback? onDoubleTap;
  final Function(DragUpdateDetails) onDragUpdate;

  const MindMapNodeWidget({
    super.key,
    required this.node,
    required this.isSelected,
    required this.isEditMode,
    required this.onTap,
    this.onDoubleTap,
    required this.onDragUpdate,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onDoubleTap: onDoubleTap,
      onPanUpdate: isEditMode ? onDragUpdate : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: node.size.width,
        height: node.size.height,
        decoration: BoxDecoration(
          color: node.color,
          borderRadius: BorderRadius.circular(8),
          border: isSelected
              ? Border.all(
                  color: Colors.white,
                  width: 3,
                )
              : null,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: isSelected ? 8 : 4,
              offset: Offset(0, isSelected ? 4 : 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (node.icon != null) ...[
                    Icon(
                      node.icon,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(height: 4),
                  ],
                  Flexible(
                    child: Text(
                      node.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (node.subtitle != null) ...[
                    const SizedBox(height: 2),
                    Flexible(
                      child: Text(
                        node.subtitle!,
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontSize: 10,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
