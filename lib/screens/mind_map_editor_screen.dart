import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/mind_map_provider.dart';
import '../widgets/mind_map_canvas.dart';
import '../widgets/loading_overlay.dart';
import '../widgets/error_dialog.dart';
import '../widgets/node_editor_dialog.dart';
import '../models/mind_map_node.dart';
import '../models/mind_map.dart';

/// Screen for editing and viewing mind maps
class MindMapEditorScreen extends StatefulWidget {
  const MindMapEditorScreen({super.key});

  @override
  State<MindMapEditorScreen> createState() => _MindMapEditorScreenState();
}

class _MindMapEditorScreenState extends State<MindMapEditorScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Consumer<MindMapProvider>(
          builder: (context, provider, child) {
            return Text(
              provider.currentMindMap?.title ?? 'Mind Map',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            );
          },
        ),
        actions: [
          Consumer<MindMapProvider>(
            builder: (context, provider, child) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(
                      provider.isEditMode ? Icons.check : Icons.edit,
                    ),
                    onPressed: provider.toggleEditMode,
                    tooltip: provider.isEditMode ? 'Done' : 'Edit',
                  ),
                  IconButton(
                    icon: const Icon(Icons.zoom_out_map),
                    onPressed: provider.resetView,
                    tooltip: 'Reset View',
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleMenuAction(value, provider),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'save',
                        child: Row(
                          children: [
                            Icon(Icons.save),
                            SizedBox(width: 8),
                            Text('Save'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'export',
                        child: Row(
                          children: [
                            Icon(Icons.download),
                            SizedBox(width: 8),
                            Text('Export'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'share',
                        child: Row(
                          children: [
                            Icon(Icons.share),
                            SizedBox(width: 8),
                            Text('Share'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'settings',
                        child: Row(
                          children: [
                            Icon(Icons.settings),
                            SizedBox(width: 8),
                            Text('Settings'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: Consumer<MindMapProvider>(
        builder: (context, provider, child) {
          if (provider.currentMindMap == null) {
            return const Center(
              child: Text('No mind map loaded'),
            );
          }

          return Stack(
            children: [
              const MindMapCanvas(),
              if (provider.isLoading)
                LoadingOverlay(
                  message: _getLoadingMessage(provider),
                ),
              if (provider.error != null)
                Positioned(
                  top: 16,
                  left: 16,
                  right: 16,
                  child: Card(
                    color: Theme.of(context).colorScheme.errorContainer,
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Theme.of(context).colorScheme.error,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              provider.error!,
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.onErrorContainer,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: provider.clearError,
                            color: Theme.of(context).colorScheme.error,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
      floatingActionButton: Consumer<MindMapProvider>(
        builder: (context, provider, child) {
          if (!provider.isEditMode) return const SizedBox.shrink();

          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              FloatingActionButton(
                heroTag: 'add_node',
                onPressed: () => _addNode(provider),
                tooltip: 'Add Node',
                child: const Icon(Icons.add),
              ),
              if (provider.selectedNodeId != null) ...[
                const SizedBox(height: 8),
                FloatingActionButton(
                  heroTag: 'delete_node',
                  onPressed: () => _deleteSelectedNode(provider),
                  tooltip: 'Delete Node',
                  backgroundColor: Theme.of(context).colorScheme.error,
                  child: const Icon(Icons.delete),
                ),
              ],
            ],
          );
        },
      ),
    );
  }

  void _handleMenuAction(String action, MindMapProvider provider) {
    switch (action) {
      case 'save':
        _saveMindMap(provider);
        break;
      case 'export':
        _exportMindMap(provider);
        break;
      case 'share':
        _shareMindMap(provider);
        break;
      case 'settings':
        _showSettings(provider);
        break;
    }
  }

  Future<void> _saveMindMap(MindMapProvider provider) async {
    await provider.saveMindMap();
    
    if (provider.error == null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Mind map saved successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _exportMindMap(MindMapProvider provider) async {
    final file = await provider.exportAsImage();
    
    if (file != null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Exported to ${file.path}'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _shareMindMap(MindMapProvider provider) async {
    await provider.shareMindMap();
  }

  void _showSettings(MindMapProvider provider) {
    showDialog(
      context: context,
      builder: (context) => _MindMapSettingsDialog(provider: provider),
    );
  }

  Future<void> _addNode(MindMapProvider provider) async {
    final parentId = provider.selectedNodeId ?? provider.currentMindMap?.rootNodeId;

    final result = await showDialog<MindMapNode>(
      context: context,
      builder: (context) => NodeEditorDialog(parentId: parentId),
    );

    if (result != null) {
      provider.addNode(result, parentId: parentId);
    }
  }

  Future<void> _deleteSelectedNode(MindMapProvider provider) async {
    final confirmed = await ErrorDialog.showConfirmation(
      context,
      title: 'Delete Node',
      message: 'Are you sure you want to delete this node and all its children?',
      confirmText: 'Delete',
      isDestructive: true,
    );

    if (confirmed && provider.selectedNodeId != null) {
      provider.removeNode(provider.selectedNodeId!);
    }
  }

  String _getLoadingMessage(MindMapProvider provider) {
    // Return appropriate loading message based on current operation
    return 'Processing...';
  }
}

/// Dialog for mind map settings and customization
class _MindMapSettingsDialog extends StatefulWidget {
  final MindMapProvider provider;

  const _MindMapSettingsDialog({required this.provider});

  @override
  State<_MindMapSettingsDialog> createState() => _MindMapSettingsDialogState();
}

class _MindMapSettingsDialogState extends State<_MindMapSettingsDialog> {
  @override
  Widget build(BuildContext context) {
    final mindMap = widget.provider.currentMindMap;
    if (mindMap == null) return const SizedBox.shrink();

    return AlertDialog(
      title: const Text('إعدادات خريطة العقل'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildLayoutSection(mindMap),
            const SizedBox(height: 16),
            _buildThemeSection(mindMap),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }

  Widget _buildLayoutSection(dynamic mindMap) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تخطيط الخريطة',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: MindMapLayout.values.map((layout) {
            final isSelected = mindMap.layout == layout;
            return ChoiceChip(
              label: Text(_getLayoutName(layout)),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  widget.provider.updateLayout(layout);
                  setState(() {});
                }
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildThemeSection(dynamic mindMap) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نمط الألوان',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 60,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: MindMapTheme.predefinedThemes.length,
            itemBuilder: (context, index) {
              final theme = MindMapTheme.predefinedThemes[index];
              final isSelected = mindMap.theme.name == theme.name;

              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: GestureDetector(
                  onTap: () {
                    widget.provider.updateTheme(theme);
                    setState(() {});
                  },
                  child: Container(
                    width: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Colors.grey.withValues(alpha: 0.3),
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [theme.primaryColor, theme.secondaryColor],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(7),
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(4),
                          child: Text(
                            theme.name,
                            style: Theme.of(context).textTheme.bodySmall,
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  String _getLayoutName(MindMapLayout layout) {
    switch (layout) {
      case MindMapLayout.radial:
        return 'شعاعي';
      case MindMapLayout.tree:
        return 'شجري';
      case MindMapLayout.organizational:
        return 'تنظيمي';
      case MindMapLayout.flowchart:
        return 'مخطط انسيابي';
      default:
        return 'شعاعي';
    }
  }
}
