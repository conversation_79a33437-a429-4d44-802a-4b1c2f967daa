import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/mind_map_provider.dart';
import '../widgets/mind_map_canvas.dart';
import '../widgets/loading_overlay.dart';
import '../widgets/error_dialog.dart';

/// Screen for editing and viewing mind maps
class MindMapEditorScreen extends StatefulWidget {
  const MindMapEditorScreen({super.key});

  @override
  State<MindMapEditorScreen> createState() => _MindMapEditorScreenState();
}

class _MindMapEditorScreenState extends State<MindMapEditorScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Consumer<MindMapProvider>(
          builder: (context, provider, child) {
            return Text(
              provider.currentMindMap?.title ?? 'Mind Map',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            );
          },
        ),
        actions: [
          Consumer<MindMapProvider>(
            builder: (context, provider, child) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(
                      provider.isEditMode ? Icons.check : Icons.edit,
                    ),
                    onPressed: provider.toggleEditMode,
                    tooltip: provider.isEditMode ? 'Done' : 'Edit',
                  ),
                  IconButton(
                    icon: const Icon(Icons.zoom_out_map),
                    onPressed: provider.resetView,
                    tooltip: 'Reset View',
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleMenuAction(value, provider),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'save',
                        child: Row(
                          children: [
                            Icon(Icons.save),
                            SizedBox(width: 8),
                            Text('Save'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'export',
                        child: Row(
                          children: [
                            Icon(Icons.download),
                            SizedBox(width: 8),
                            Text('Export'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'share',
                        child: Row(
                          children: [
                            Icon(Icons.share),
                            SizedBox(width: 8),
                            Text('Share'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'settings',
                        child: Row(
                          children: [
                            Icon(Icons.settings),
                            SizedBox(width: 8),
                            Text('Settings'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: Consumer<MindMapProvider>(
        builder: (context, provider, child) {
          if (provider.currentMindMap == null) {
            return const Center(
              child: Text('No mind map loaded'),
            );
          }

          return Stack(
            children: [
              const MindMapCanvas(),
              if (provider.isLoading)
                LoadingOverlay(
                  message: _getLoadingMessage(provider),
                ),
              if (provider.error != null)
                Positioned(
                  top: 16,
                  left: 16,
                  right: 16,
                  child: Card(
                    color: Theme.of(context).colorScheme.errorContainer,
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Theme.of(context).colorScheme.error,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              provider.error!,
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.onErrorContainer,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: provider.clearError,
                            color: Theme.of(context).colorScheme.error,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
      floatingActionButton: Consumer<MindMapProvider>(
        builder: (context, provider, child) {
          if (!provider.isEditMode) return const SizedBox.shrink();

          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              FloatingActionButton(
                heroTag: 'add_node',
                onPressed: () => _addNode(provider),
                tooltip: 'Add Node',
                child: const Icon(Icons.add),
              ),
              if (provider.selectedNodeId != null) ...[
                const SizedBox(height: 8),
                FloatingActionButton(
                  heroTag: 'delete_node',
                  onPressed: () => _deleteSelectedNode(provider),
                  tooltip: 'Delete Node',
                  backgroundColor: Theme.of(context).colorScheme.error,
                  child: const Icon(Icons.delete),
                ),
              ],
            ],
          );
        },
      ),
    );
  }

  void _handleMenuAction(String action, MindMapProvider provider) {
    switch (action) {
      case 'save':
        _saveMindMap(provider);
        break;
      case 'export':
        _exportMindMap(provider);
        break;
      case 'share':
        _shareMindMap(provider);
        break;
      case 'settings':
        _showSettings(provider);
        break;
    }
  }

  Future<void> _saveMindMap(MindMapProvider provider) async {
    await provider.saveMindMap();
    
    if (provider.error == null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Mind map saved successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _exportMindMap(MindMapProvider provider) async {
    final file = await provider.exportAsImage();
    
    if (file != null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Exported to ${file.path}'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _shareMindMap(MindMapProvider provider) async {
    await provider.shareMindMap();
  }

  void _showSettings(MindMapProvider provider) {
    // TODO: Implement settings dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Settings'),
        content: const Text('Settings panel coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _addNode(MindMapProvider provider) {
    // TODO: Implement add node dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Node'),
        content: const Text('Add node functionality coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteSelectedNode(MindMapProvider provider) async {
    final confirmed = await ErrorDialog.showConfirmation(
      context,
      title: 'Delete Node',
      message: 'Are you sure you want to delete this node and all its children?',
      confirmText: 'Delete',
      isDestructive: true,
    );

    if (confirmed && provider.selectedNodeId != null) {
      provider.removeNode(provider.selectedNodeId!);
    }
  }

  String _getLoadingMessage(MindMapProvider provider) {
    // Return appropriate loading message based on current operation
    return 'Processing...';
  }
}
