import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../providers/mind_map_provider.dart';
import '../models/mind_map.dart';
import '../widgets/mind_map_card.dart';
import '../widgets/loading_overlay.dart';
import '../widgets/error_dialog.dart';
import 'mind_map_editor_screen.dart';
import 'pdf_import_screen.dart';
import 'settings_screen.dart';

/// Home screen displaying saved mind maps and import options
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSavedMindMaps();
    });
  }

  Future<void> _loadSavedMindMaps() async {
    final provider = context.read<MindMapProvider>();
    await provider.loadSavedMindMaps();
  }

  Future<void> _importPDF() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty && mounted) {
        final file = File(result.files.first.path!);

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PDFImportScreen(pdfFile: file),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ErrorDialog.show(
          context,
          title: 'Import Error',
          message: 'Failed to import PDF file: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _createNewMindMap() async {
    final result = await showDialog<String>(
      context: context,
      builder: (context) => _NewMindMapDialog(),
    );

    if (result != null && result.isNotEmpty && mounted) {
      final provider = context.read<MindMapProvider>();
      provider.createNewMindMap(result);

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const MindMapEditorScreen(),
        ),
      );
    }
  }

  Future<void> _openMindMap(MindMap mindMap) async {
    final provider = context.read<MindMapProvider>();
    await provider.loadMindMap(mindMap.id);
    
    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const MindMapEditorScreen(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Maper'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSavedMindMaps,
            tooltip: 'Refresh',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SettingsScreen(),
                ),
              );
            },
            tooltip: 'Settings',
          ),
        ],
      ),
      body: Consumer<MindMapProvider>(
        builder: (context, provider, child) {
          return Stack(
            children: [
              _buildBody(provider),
              if (provider.isLoading) const LoadingOverlay(),
            ],
          );
        },
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton(
            heroTag: 'import_pdf',
            onPressed: _importPDF,
            tooltip: 'Import PDF',
            child: const Icon(Icons.picture_as_pdf),
          ),
          const SizedBox(height: 16),
          FloatingActionButton(
            heroTag: 'new_mind_map',
            onPressed: _createNewMindMap,
            tooltip: 'New Mind Map',
            child: const Icon(Icons.add),
          ),
        ],
      ),
    );
  }

  Widget _buildBody(MindMapProvider provider) {
    if (provider.error != null) {
      return _buildErrorState(provider);
    }

    if (provider.savedMindMaps.isEmpty) {
      return _buildEmptyState();
    }

    return _buildMindMapsList(provider.savedMindMaps);
  }

  Widget _buildErrorState(MindMapProvider provider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading mind maps',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            provider.error!,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              provider.clearError();
              _loadSavedMindMaps();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_tree,
            size: 96,
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 24),
          Text(
            'No Mind Maps Yet',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first mind map by importing a PDF\nor starting from scratch',
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    onPressed: _importPDF,
                    icon: const Icon(Icons.picture_as_pdf),
                    label: const Text('Import PDF'),
                  ),
                  const SizedBox(width: 16),
                  OutlinedButton.icon(
                    onPressed: _createNewMindMap,
                    icon: const Icon(Icons.add),
                    label: const Text('New Mind Map'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextButton.icon(
                onPressed: _createDemoMindMap,
                icon: const Icon(Icons.psychology),
                label: const Text('إنشاء خريطة تجريبية'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMindMapsList(List<MindMap> mindMaps) {
    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: mindMaps.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 375),
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: MindMapCard(
                    mindMap: mindMaps[index],
                    onTap: () => _openMindMap(mindMaps[index]),
                    onDelete: () => _deleteMindMap(mindMaps[index]),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _deleteMindMap(MindMap mindMap) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Mind Map'),
        content: Text('Are you sure you want to delete "${mindMap.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final provider = context.read<MindMapProvider>();
      await provider.deleteMindMap(mindMap.id);
    }
  }
}

class _NewMindMapDialog extends StatefulWidget {
  @override
  State<_NewMindMapDialog> createState() => _NewMindMapDialogState();
}

class _NewMindMapDialogState extends State<_NewMindMapDialog> {
  final _controller = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('New Mind Map'),
      content: Form(
        key: _formKey,
        child: TextFormField(
          controller: _controller,
          decoration: const InputDecoration(
            labelText: 'Mind Map Title',
            hintText: 'Enter a title for your mind map',
          ),
          autofocus: true,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter a title';
            }
            return null;
          },
          onFieldSubmitted: (_) => _submit(),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _submit,
          child: const Text('Create'),
        ),
      ],
    );
  }

  void _submit() {
    if (_formKey.currentState!.validate()) {
      Navigator.pop(context, _controller.text.trim());
    }
  }
}
