import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/mind_map_provider.dart';
import '../models/mind_map_node.dart';

/// شاشة تشخيص بسيطة لاختبار عرض الخرائط الذهنية
class DebugMindMapScreen extends StatefulWidget {
  const DebugMindMapScreen({super.key});

  @override
  State<DebugMindMapScreen> createState() => _DebugMindMapScreenState();
}

class _DebugMindMapScreenState extends State<DebugMindMapScreen> {
  @override
  void initState() {
    super.initState();
    _createTestMindMap();
  }

  void _createTestMindMap() {
    final provider = context.read<MindMapProvider>();
    
    // إنشاء خريطة عقل بسيطة
    provider.createNewMindMap('اختبار الخريطة');
    
    // إضافة عقد بسيطة
    final rootNode = provider.currentMindMap?.rootNode;
    if (rootNode != null) {
      // عقدة في المنتصف
      final centerNode = MindMapNode(
        title: 'وسط',
        color: Colors.blue,
        position: const Offset(0, 0),
        level: 1,
      );
      provider.addNode(centerNode, parentId: rootNode.id);
      
      // عقدة يمين
      final rightNode = MindMapNode(
        title: 'يمين',
        color: Colors.green,
        position: const Offset(200, 0),
        level: 1,
      );
      provider.addNode(rightNode, parentId: rootNode.id);
      
      // عقدة يسار
      final leftNode = MindMapNode(
        title: 'يسار',
        color: Colors.red,
        position: const Offset(-200, 0),
        level: 1,
      );
      provider.addNode(leftNode, parentId: rootNode.id);
      
      // عقدة أعلى
      final topNode = MindMapNode(
        title: 'أعلى',
        color: Colors.orange,
        position: const Offset(0, -200),
        level: 1,
      );
      provider.addNode(topNode, parentId: rootNode.id);
      
      // عقدة أسفل
      final bottomNode = MindMapNode(
        title: 'أسفل',
        color: Colors.purple,
        position: const Offset(0, 200),
        level: 1,
      );
      provider.addNode(bottomNode, parentId: rootNode.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الخريطة الذهنية'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Consumer<MindMapProvider>(
        builder: (context, provider, child) {
          final mindMap = provider.currentMindMap;
          
          if (mindMap == null) {
            return const Center(
              child: Text('لا توجد خريطة عقل'),
            );
          }

          return Container(
            color: Colors.grey[100],
            child: InteractiveViewer(
              boundaryMargin: const EdgeInsets.all(100),
              minScale: 0.1,
              maxScale: 3.0,
              child: Container(
                width: 2000,
                height: 2000,
                color: Colors.white,
                child: Stack(
                  children: [
                    // رسم شبكة للمساعدة في التشخيص
                    CustomPaint(
                      size: const Size(2000, 2000),
                      painter: GridPainter(),
                    ),
                    
                    // رسم العقد
                    for (final node in mindMap.nodes.values) ...[
                      Builder(
                        builder: (context) {
                          final left = node.position.dx + 1000 - node.size.width / 2;
                          final top = node.position.dy + 1000 - node.size.height / 2;

                          return Positioned(
                            left: left,
                            top: top,
                            child: Container(
                              width: node.size.width,
                              height: node.size.height,
                              decoration: BoxDecoration(
                                color: node.color,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.black, width: 2),
                                boxShadow: const [
                                  BoxShadow(
                                    color: Colors.black26,
                                    blurRadius: 4,
                                    offset: Offset(2, 2),
                                  ),
                                ],
                              ),
                              child: Center(
                                child: Text(
                                  node.title,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                    
                    // نقطة المركز
                    Positioned(
                      left: 1000 - 5,
                      top: 1000 - 5,
                      child: Container(
                        width: 10,
                        height: 10,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton(
            heroTag: 'info',
            onPressed: _showInfo,
            child: const Icon(Icons.info),
          ),
          const SizedBox(height: 8),
          FloatingActionButton(
            heroTag: 'refresh',
            onPressed: _createTestMindMap,
            child: const Icon(Icons.refresh),
          ),
        ],
      ),
    );
  }

  void _showInfo() {
    final provider = context.read<MindMapProvider>();
    final mindMap = provider.currentMindMap;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات الخريطة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('عدد العقد: ${mindMap?.nodes.length ?? 0}'),
            const SizedBox(height: 8),
            if (mindMap != null) ...[
              const Text('العقد:'),
              ...mindMap.nodes.values.map((node) => Text(
                '- ${node.title}: ${node.position}',
              )),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}

/// رسام الشبكة للمساعدة في التشخيص
class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.3)
      ..strokeWidth = 1;

    // رسم خطوط عمودية
    for (double x = 0; x < size.width; x += 100) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // رسم خطوط أفقية
    for (double y = 0; y < size.height; y += 100) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // رسم خط المركز العمودي
    final centerPaint = Paint()
      ..color = Colors.red.withValues(alpha: 0.5)
      ..strokeWidth = 2;
    
    canvas.drawLine(
      Offset(size.width / 2, 0),
      Offset(size.width / 2, size.height),
      centerPaint,
    );

    // رسم خط المركز الأفقي
    canvas.drawLine(
      Offset(0, size.height / 2),
      Offset(size.width, size.height / 2),
      centerPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
