import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/mind_map_provider.dart';
import '../models/mind_map.dart';
import '../services/pdf_parser_service.dart';
import '../widgets/loading_overlay.dart';
import '../widgets/error_dialog.dart';
import 'mind_map_editor_screen.dart';

/// Screen for importing and configuring PDF to mind map conversion
class PDFImportScreen extends StatefulWidget {
  final File pdfFile;

  const PDFImportScreen({
    super.key,
    required this.pdfFile,
  });

  @override
  State<PDFImportScreen> createState() => _PDFImportScreenState();
}

class _PDFImportScreenState extends State<PDFImportScreen> {
  Map<String, dynamic>? _pdfInfo;
  MindMapLayout _selectedLayout = MindMapLayout.radial;
  MindMapTheme _selectedTheme = MindMapTheme.predefinedThemes[0];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadPDFInfo();
  }

  Future<void> _loadPDFInfo() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final info = await PDFParserService.getPDFInfo(widget.pdfFile);
      setState(() {
        _pdfInfo = info;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createMindMap() async {
    final provider = context.read<MindMapProvider>();
    
    await provider.createMindMapFromPDF(
      widget.pdfFile,
      layout: _selectedLayout,
      theme: _selectedTheme,
    );

    if (provider.error != null) {
      if (mounted) {
        ErrorDialog.show(
          context,
          title: 'Import Error',
          message: provider.error!,
        );
      }
      return;
    }

    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => const MindMapEditorScreen(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Import PDF'),
        actions: [
          if (_pdfInfo != null)
            TextButton(
              onPressed: _createMindMap,
              child: const Text(
                'CREATE',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Consumer<MindMapProvider>(
        builder: (context, provider, child) {
          return Stack(
            children: [
              _buildBody(),
              if (_isLoading || provider.isLoading)
                LoadingOverlay(
                  message: provider.isLoading 
                      ? 'Creating mind map...' 
                      : 'Loading PDF info...',
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildBody() {
    if (_error != null) {
      return _buildErrorState();
    }

    if (_pdfInfo == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPDFInfoCard(),
          const SizedBox(height: 24),
          _buildLayoutSelection(),
          const SizedBox(height: 24),
          _buildThemeSelection(),
          const SizedBox(height: 32),
          _buildCreateButton(),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading PDF',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadPDFInfo,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildPDFInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.picture_as_pdf,
                  color: Colors.red,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'PDF Document',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      Text(
                        _pdfInfo!['fileName'],
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildInfoItem(
                  'Pages',
                  '${_pdfInfo!['pageCount']}',
                  Icons.description,
                ),
                const SizedBox(width: 24),
                _buildInfoItem(
                  'Size',
                  _formatFileSize(_pdfInfo!['fileSize']),
                  Icons.storage,
                ),
              ],
            ),
            if (_pdfInfo!['title'] != null && _pdfInfo!['title'].isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'Title: ${_pdfInfo!['title']}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
            if (_pdfInfo!['author'] != null && _pdfInfo!['author'].isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                'Author: ${_pdfInfo!['author']}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Theme.of(context).colorScheme.primary),
        const SizedBox(width: 4),
        Text(
          '$label: $value',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildLayoutSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Layout Style',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: MindMapLayout.values.map((layout) {
            final isSelected = _selectedLayout == layout;
            return ChoiceChip(
              label: Text(_getLayoutName(layout)),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _selectedLayout = layout;
                  });
                }
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildThemeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Color Theme',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: MindMapTheme.predefinedThemes.length,
            itemBuilder: (context, index) {
              final theme = MindMapTheme.predefinedThemes[index];
              final isSelected = _selectedTheme == theme;
              
              return Padding(
                padding: const EdgeInsets.only(right: 12),
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedTheme = theme;
                    });
                  },
                  child: Container(
                    width: 120,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected 
                            ? Theme.of(context).colorScheme.primary
                            : Colors.grey.withOpacity(0.3),
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [theme.primaryColor, theme.secondaryColor],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(7),
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: Text(
                            theme.name,
                            style: Theme.of(context).textTheme.bodySmall,
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCreateButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _createMindMap,
        icon: const Icon(Icons.auto_awesome),
        label: const Text('Create Mind Map'),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }

  String _getLayoutName(MindMapLayout layout) {
    switch (layout) {
      case MindMapLayout.radial:
        return 'Radial';
      case MindMapLayout.tree:
        return 'Tree';
      case MindMapLayout.organizational:
        return 'Organizational';
      case MindMapLayout.flowchart:
        return 'Flowchart';
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}
