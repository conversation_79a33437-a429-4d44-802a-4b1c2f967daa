import 'package:flutter/material.dart';
import '../services/file_manager_service.dart';
import '../widgets/error_dialog.dart';
import '../widgets/loading_overlay.dart';

/// Settings screen for app configuration and management
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _storageInfo;

  @override
  void initState() {
    super.initState();
    _loadStorageInfo();
  }

  Future<void> _loadStorageInfo() async {
    setState(() => _isLoading = true);
    try {
      final info = await FileManagerService.getStorageInfo();
      setState(() => _storageInfo = info);
    } catch (e) {
      if (mounted) {
        ErrorDialog.show(
          context,
          title: 'خطأ',
          message: 'فشل في تحميل معلومات التخزين: ${e.toString()}',
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        centerTitle: true,
      ),
      body: Stack(
        children: [
          _buildBody(),
          if (_isLoading) const LoadingOverlay(),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildStorageSection(),
        const SizedBox(height: 24),
        _buildDataManagementSection(),
        const SizedBox(height: 24),
        _buildAboutSection(),
      ],
    );
  }

  Widget _buildStorageSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.storage,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'معلومات التخزين',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_storageInfo != null) ...[
              _buildStorageItem(
                'خرائط العقل',
                '${_storageInfo!['mindMapsCount']} ملف',
                _formatFileSize(_storageInfo!['mindMapsSize']),
              ),
              const SizedBox(height: 8),
              _buildStorageItem(
                'الملفات المُصدرة',
                '${_storageInfo!['exportsCount']} ملف',
                _formatFileSize(_storageInfo!['exportsSize']),
              ),
              const SizedBox(height: 8),
              _buildStorageItem(
                'المجموع الكلي',
                '',
                _formatFileSize(_storageInfo!['totalSize']),
                isTotal: true,
              ),
            ] else
              const Center(child: CircularProgressIndicator()),
          ],
        ),
      ),
    );
  }

  Widget _buildStorageItem(String title, String subtitle, String size, {bool isTotal = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        border: isTotal ? Border(
          top: BorderSide(color: Theme.of(context).dividerColor),
        ) : null,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              if (subtitle.isNotEmpty)
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
            ],
          ),
          Text(
            size,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Theme.of(context).colorScheme.primary : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataManagementSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.manage_accounts,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'إدارة البيانات',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildActionTile(
              icon: Icons.backup,
              title: 'إنشاء نسخة احتياطية',
              subtitle: 'حفظ جميع خرائط العقل',
              onTap: _createBackup,
            ),
            _buildActionTile(
              icon: Icons.restore,
              title: 'استعادة من نسخة احتياطية',
              subtitle: 'استعادة خرائط العقل من ملف',
              onTap: _restoreBackup,
            ),
            _buildActionTile(
              icon: Icons.clear_all,
              title: 'مسح الملفات المُصدرة',
              subtitle: 'حذف جميع الملفات المُصدرة',
              onTap: _clearExports,
              isDestructive: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive 
            ? Theme.of(context).colorScheme.error 
            : Theme.of(context).colorScheme.primary,
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildAboutSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'حول التطبيق',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('الاسم', 'Maper - محول PDF إلى خريطة عقل'),
            _buildInfoRow('الإصدار', '1.0.0'),
            _buildInfoRow('المطور', 'Augment Agent'),
            const SizedBox(height: 16),
            Text(
              'تطبيق احترافي لتحويل مستندات PDF إلى خرائط عقل تفاعلية وجذابة بصرياً.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _createBackup() async {
    setState(() => _isLoading = true);
    try {
      final file = await FileManagerService.createBackup();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إنشاء النسخة الاحتياطية: ${file.path}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ErrorDialog.show(
          context,
          title: 'خطأ',
          message: 'فشل في إنشاء النسخة الاحتياطية: ${e.toString()}',
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _restoreBackup() async {
    // TODO: Implement file picker for backup restoration
    ErrorDialog.show(
      context,
      title: 'قريباً',
      message: 'ميزة استعادة النسخة الاحتياطية ستكون متاحة قريباً.',
    );
  }

  Future<void> _clearExports() async {
    final confirmed = await ErrorDialog.showConfirmation(
      context,
      title: 'تأكيد الحذف',
      message: 'هل أنت متأكد من حذف جميع الملفات المُصدرة؟',
      confirmText: 'حذف',
      isDestructive: true,
    );

    if (confirmed) {
      setState(() => _isLoading = true);
      try {
        await FileManagerService.clearExports();
        await _loadStorageInfo();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف جميع الملفات المُصدرة'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ErrorDialog.show(
            context,
            title: 'خطأ',
            message: 'فشل في حذف الملفات: ${e.toString()}',
          );
        }
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes بايت';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} كيلوبايت';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
  }
}
