import 'package:flutter/foundation.dart';

/// Application configuration and environment settings
class AppConfig {
  static const String appName = 'Maper';
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';
  
  // Environment settings
  static bool get isDebug => kDebugMode;
  static bool get isRelease => kReleaseMode;
  static bool get isProfile => kProfileMode;
  
  // Feature flags
  static const bool enableAdvancedFeatures = true;
  static const bool enableExperimentalFeatures = false;
  static const bool enableAnalytics = false;
  static const bool enableCrashReporting = true;
  
  // Performance settings
  static const int maxConcurrentOperations = 3;
  static const Duration networkTimeout = Duration(seconds: 30);
  static const Duration fileOperationTimeout = Duration(seconds: 60);
  
  // PDF processing settings
  static const int maxPdfFileSize = 50 * 1024 * 1024; // 50MB
  static const int maxPdfPages = 500;
  static const int maxTextLengthPerPage = 10000;
  
  // Mind map settings
  static const int maxNodesPerMindMap = 1000;
  static const int maxMindMapDepth = 6;
  static const int maxChildrenPerNode = 10;
  
  // UI settings
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration shortAnimationDuration = Duration(milliseconds: 150);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // Cache settings
  static const int maxCacheSize = 100;
  static const Duration cacheExpiration = Duration(hours: 24);
  
  // Storage settings
  static const String mindMapsDirectory = 'mind_maps';
  static const String exportsDirectory = 'exports';
  static const String cacheDirectory = 'cache';
  static const String tempDirectory = 'temp';
  
  // Default values
  static const String defaultMindMapTitle = 'خريطة عقل جديدة';
  static const String defaultNodeTitle = 'عقدة جديدة';
  static const String defaultRootNodeTitle = 'الموضوع الرئيسي';
  
  // Supported formats
  static const List<String> supportedPdfExtensions = ['pdf'];
  static const List<String> supportedImageExtensions = ['png', 'jpg', 'jpeg'];
  static const List<String> supportedExportFormats = ['png', 'jpg', 'json'];
  
  // Error messages
  static const Map<String, String> errorMessages = {
    'pdf_parsing_error': 'فشل في تحليل ملف PDF',
    'file_too_large': 'حجم الملف كبير جداً',
    'invalid_pdf': 'ملف PDF غير صالح',
    'network_error': 'خطأ في الاتصال',
    'storage_error': 'خطأ في التخزين',
    'unknown_error': 'حدث خطأ غير متوقع',
  };
  
  // Success messages
  static const Map<String, String> successMessages = {
    'mind_map_saved': 'تم حفظ خريطة العقل بنجاح',
    'mind_map_exported': 'تم تصدير خريطة العقل بنجاح',
    'mind_map_shared': 'تم مشاركة خريطة العقل',
    'backup_created': 'تم إنشاء النسخة الاحتياطية',
  };
  
  // API endpoints (if needed in future)
  static const String baseUrl = '';
  static const String apiVersion = 'v1';
  
  // Analytics settings
  static const bool enableUserAnalytics = false;
  static const bool enablePerformanceAnalytics = false;
  
  // Accessibility settings
  static const double minTouchTargetSize = 44.0;
  static const double defaultFontSize = 14.0;
  static const double largeFontSize = 18.0;
  static const double extraLargeFontSize = 22.0;
  
  // Theme settings
  static const String defaultTheme = 'system';
  static const List<String> availableThemes = ['light', 'dark', 'system'];
  
  // Language settings
  static const String defaultLanguage = 'ar';
  static const List<String> supportedLanguages = ['ar', 'en'];
  
  // Security settings
  static const bool enableDataEncryption = false;
  static const bool enableBiometricAuth = false;
  
  // Development settings
  static bool get enableDebugLogging => isDebug;
  static bool get enablePerformanceLogging => isDebug;
  static bool get enableNetworkLogging => isDebug;
  
  // Get error message by key
  static String getErrorMessage(String key) {
    return errorMessages[key] ?? errorMessages['unknown_error']!;
  }
  
  // Get success message by key
  static String getSuccessMessage(String key) {
    return successMessages[key] ?? 'تمت العملية بنجاح';
  }
  
  // Check if feature is enabled
  static bool isFeatureEnabled(String feature) {
    switch (feature) {
      case 'advanced_features':
        return enableAdvancedFeatures;
      case 'experimental_features':
        return enableExperimentalFeatures;
      case 'analytics':
        return enableAnalytics;
      case 'crash_reporting':
        return enableCrashReporting;
      default:
        return false;
    }
  }
  
  // Get timeout for operation type
  static Duration getTimeout(String operationType) {
    switch (operationType) {
      case 'network':
        return networkTimeout;
      case 'file_operation':
        return fileOperationTimeout;
      case 'animation':
        return animationDuration;
      default:
        return const Duration(seconds: 30);
    }
  }
  
  // Validate file size
  static bool isFileSizeValid(int fileSize, String fileType) {
    switch (fileType) {
      case 'pdf':
        return fileSize <= maxPdfFileSize;
      default:
        return fileSize <= maxPdfFileSize;
    }
  }
  
  // Get app info
  static Map<String, dynamic> getAppInfo() {
    return {
      'name': appName,
      'version': appVersion,
      'buildNumber': buildNumber,
      'isDebug': isDebug,
      'isRelease': isRelease,
      'isProfile': isProfile,
    };
  }
}
