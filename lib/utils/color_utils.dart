import 'package:flutter/material.dart';

/// Utility class for color operations
class ColorUtils {
  /// Converts Color to int value safely
  static int colorToInt(Color color) {
    // Using the recommended approach for color conversion
    return (color.a * 255).round() << 24 |
           (color.r * 255).round() << 16 |
           (color.g * 255).round() << 8 |
           (color.b * 255).round();
  }

  /// Converts int value to Color safely
  static Color intToColor(int value) {
    return Color(value);
  }

  /// Gets color value for JSON serialization
  static int getColorValue(Color color) {
    // Using the recommended approach for color conversion
    return (color.a * 255).round() << 24 |
           (color.r * 255).round() << 16 |
           (color.g * 255).round() << 8 |
           (color.b * 255).round();
  }
  
  /// Creates color from JSON value
  static Color fromJsonValue(dynamic value, {Color defaultColor = Colors.blue}) {
    if (value is int) {
      return Color(value);
    }
    return defaultColor;
  }
  
  /// Checks if color is light or dark
  static bool isLightColor(Color color) {
    return color.computeLuminance() > 0.5;
  }
  
  /// Gets contrasting text color
  static Color getContrastingTextColor(Color backgroundColor) {
    return isLightColor(backgroundColor) ? Colors.black : Colors.white;
  }
  
  /// Creates a lighter version of the color
  static Color lighten(Color color, [double amount = 0.1]) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }
  
  /// Creates a darker version of the color
  static Color darken(Color color, [double amount = 0.1]) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }
  
  /// Creates color with alpha
  static Color withAlpha(Color color, double alpha) {
    return color.withValues(alpha: alpha);
  }
  
  /// Predefined color palette for mind map nodes
  static const List<Color> mindMapColors = [
    Color(0xFF2196F3), // Blue
    Color(0xFF4CAF50), // Green
    Color(0xFFFF9800), // Orange
    Color(0xFF9C27B0), // Purple
    Color(0xFFF44336), // Red
    Color(0xFF00BCD4), // Cyan
    Color(0xFF795548), // Brown
    Color(0xFF607D8B), // Blue Grey
    Color(0xFFE91E63), // Pink
    Color(0xFF3F51B5), // Indigo
    Color(0xFF009688), // Teal
    Color(0xFF8BC34A), // Light Green
    Color(0xFFFFEB3B), // Yellow
    Color(0xFFFF5722), // Deep Orange
    Color(0xFF673AB7), // Deep Purple
    Color(0xFF9E9E9E), // Grey
  ];
  
  /// Gets a color from the predefined palette
  static Color getPaletteColor(int index) {
    return mindMapColors[index % mindMapColors.length];
  }
  
  /// Generates a random color from the palette
  static Color getRandomPaletteColor() {
    final random = DateTime.now().millisecondsSinceEpoch % mindMapColors.length;
    return mindMapColors[random];
  }
}
