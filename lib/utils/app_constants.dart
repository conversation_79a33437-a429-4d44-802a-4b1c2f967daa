/// Application constants and configuration
class AppConstants {
  // App Information
  static const String appName = 'Maper';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Professional PDF to Mind Map Converter';
  
  // File Limits
  static const int maxPdfFileSize = 50 * 1024 * 1024; // 50MB
  static const int maxNodesPerMindMap = 1000;
  static const int maxNodeTitleLength = 50;
  static const int maxNodeSubtitleLength = 100;
  static const int maxNodeDescriptionLength = 500;
  
  // UI Constants
  static const double defaultNodeWidth = 120.0;
  static const double defaultNodeHeight = 60.0;
  static const double minZoomLevel = 0.1;
  static const double maxZoomLevel = 3.0;
  static const double defaultZoomLevel = 1.0;
  
  // Layout Constants
  static const double radialLayoutRadius = 200.0;
  static const double treeLayoutLevelHeight = 150.0;
  static const double treeLayoutNodeSpacing = 180.0;
  static const double connectionWidth = 2.0;
  
  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 375);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // Storage Keys
  static const String mindMapsStorageKey = 'mind_maps';
  static const String settingsStorageKey = 'app_settings';
  static const String lastOpenedMindMapKey = 'last_opened_mind_map';
  
  // Error Messages
  static const String pdfParsingError = 'فشل في تحليل ملف PDF';
  static const String fileTooLargeError = 'حجم الملف كبير جداً (الحد الأقصى 50 ميجابايت)';
  static const String invalidPdfError = 'ملف PDF غير صالح';
  static const String networkError = 'خطأ في الاتصال';
  static const String storageError = 'خطأ في التخزين';
  static const String unknownError = 'حدث خطأ غير متوقع';
  
  // Success Messages
  static const String mindMapSavedSuccess = 'تم حفظ خريطة العقل بنجاح';
  static const String mindMapExportedSuccess = 'تم تصدير خريطة العقل بنجاح';
  static const String mindMapSharedSuccess = 'تم مشاركة خريطة العقل';
  static const String backupCreatedSuccess = 'تم إنشاء النسخة الاحتياطية';
  
  // File Extensions
  static const List<String> supportedPdfExtensions = ['pdf'];
  static const List<String> supportedImageExtensions = ['png', 'jpg', 'jpeg'];
  static const List<String> supportedExportFormats = ['png', 'jpg', 'json'];
  
  // Default Values
  static const String defaultMindMapTitle = 'خريطة عقل جديدة';
  static const String defaultNodeTitle = 'عقدة جديدة';
  static const String defaultRootNodeTitle = 'الموضوع الرئيسي';
  
  // PDF Processing
  static const int maxPdfPages = 500;
  static const int maxTextLengthPerPage = 10000;
  static const double minHeadingConfidence = 0.7;
  
  // Mind Map Generation
  static const int maxChildrenPerNode = 10;
  static const int maxMindMapDepth = 6;
  static const double nodePositionTolerance = 5.0;
  
  // Performance Settings
  static const int maxConcurrentOperations = 3;
  static const Duration operationTimeout = Duration(seconds: 30);
  static const int maxCacheSize = 100;
  
  // UI Responsive Breakpoints
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;
  
  // Accessibility
  static const double minTouchTargetSize = 44.0;
  static const double defaultFontSize = 14.0;
  static const double largeFontSize = 18.0;
  
  // Debug Settings
  static const bool enableDebugMode = false;
  static const bool enablePerformanceLogging = false;
  static const bool enableCrashReporting = true;
}

/// Application routes
class AppRoutes {
  static const String home = '/';
  static const String pdfImport = '/pdf-import';
  static const String mindMapEditor = '/mind-map-editor';
  static const String settings = '/settings';
  static const String about = '/about';
}

/// Application themes
class AppThemes {
  static const String light = 'light';
  static const String dark = 'dark';
  static const String system = 'system';
}

/// Mind map export formats
enum ExportFormat {
  png,
  jpeg,
  json,
  svg,
}

/// Application languages
enum AppLanguage {
  arabic,
  english,
}

/// Mind map complexity levels
enum ComplexityLevel {
  simple,
  medium,
  complex,
  detailed,
}

/// PDF processing quality
enum ProcessingQuality {
  fast,
  balanced,
  accurate,
}
