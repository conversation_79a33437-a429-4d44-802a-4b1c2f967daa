import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../utils/color_utils.dart';

/// Represents a single node in the mind map
class MindMapNode {
  final String id;
  String title;
  String? subtitle;
  String? description;
  Color color;
  IconData? icon;
  Offset position;
  Size size;
  List<String> childrenIds;
  String? parentId;
  int level;
  bool isExpanded;
  bool isSelected;
  DateTime createdAt;
  DateTime updatedAt;

  MindMapNode({
    String? id,
    required this.title,
    this.subtitle,
    this.description,
    this.color = Colors.blue,
    this.icon,
    this.position = Offset.zero,
    this.size = const Size(120, 60),
    List<String>? childrenIds,
    this.parentId,
    this.level = 0,
    this.isExpanded = true,
    this.isSelected = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        childrenIds = childrenIds ?? [],
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Creates a copy of this node with updated properties
  MindMapNode copyWith({
    String? title,
    String? subtitle,
    String? description,
    Color? color,
    IconData? icon,
    Offset? position,
    Size? size,
    List<String>? childrenIds,
    String? parentId,
    int? level,
    bool? isExpanded,
    bool? isSelected,
  }) {
    return MindMapNode(
      id: id,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      description: description ?? this.description,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      position: position ?? this.position,
      size: size ?? this.size,
      childrenIds: childrenIds ?? List.from(this.childrenIds),
      parentId: parentId ?? this.parentId,
      level: level ?? this.level,
      isExpanded: isExpanded ?? this.isExpanded,
      isSelected: isSelected ?? this.isSelected,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  /// Converts the node to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'subtitle': subtitle,
      'description': description,
      'color': color.value,
      'icon': icon?.codePoint,
      'position': {'x': position.dx, 'y': position.dy},
      'size': {'width': size.width, 'height': size.height},
      'childrenIds': childrenIds,
      'parentId': parentId,
      'level': level,
      'isExpanded': isExpanded,
      'isSelected': isSelected,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Creates a node from a JSON map
  factory MindMapNode.fromJson(Map<String, dynamic> json) {
    return MindMapNode(
      id: json['id'],
      title: json['title'],
      subtitle: json['subtitle'],
      description: json['description'],
      color: Color(json['color'] ?? Colors.blue.value),
      icon: json['icon'] != null ? IconData(json['icon'], fontFamily: 'MaterialIcons') : null,
      position: Offset(json['position']['x'], json['position']['y']),
      size: Size(json['size']['width'], json['size']['height']),
      childrenIds: List<String>.from(json['childrenIds'] ?? []),
      parentId: json['parentId'],
      level: json['level'] ?? 0,
      isExpanded: json['isExpanded'] ?? true,
      isSelected: json['isSelected'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MindMapNode && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MindMapNode(id: $id, title: $title, level: $level)';
  }
}
