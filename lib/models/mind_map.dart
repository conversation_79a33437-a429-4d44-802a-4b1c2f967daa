import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'mind_map_node.dart';

/// Layout types for mind map visualization
enum MindMapLayout {
  radial,
  tree,
  organizational,
  flowchart,
}

/// Theme configuration for mind map appearance
class MindMapTheme {
  final String name;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color connectionColor;
  final double connectionWidth;
  final TextStyle nodeTextStyle;
  final BorderRadius nodeBorderRadius;

  const MindMapTheme({
    required this.name,
    required this.primaryColor,
    required this.secondaryColor,
    required this.backgroundColor,
    required this.connectionColor,
    this.connectionWidth = 2.0,
    required this.nodeTextStyle,
    this.nodeBorderRadius = const BorderRadius.all(Radius.circular(8)),
  });

  static const List<MindMapTheme> predefinedThemes = [
    MindMapTheme(
      name: 'Professional Blue',
      primaryColor: Color(0xFF2196F3),
      secondaryColor: Color(0xFF64B5F6),
      backgroundColor: Color(0xFFF5F5F5),
      connectionColor: Color(0xFF1976D2),
      nodeTextStyle: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
    ),
    MindMapTheme(
      name: 'Nature Green',
      primaryColor: Color(0xFF4CAF50),
      secondaryColor: Color(0xFF81C784),
      backgroundColor: Color(0xFFF1F8E9),
      connectionColor: Color(0xFF388E3C),
      nodeTextStyle: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
    ),
    MindMapTheme(
      name: 'Creative Purple',
      primaryColor: Color(0xFF9C27B0),
      secondaryColor: Color(0xFFBA68C8),
      backgroundColor: Color(0xFFF3E5F5),
      connectionColor: Color(0xFF7B1FA2),
      nodeTextStyle: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
    ),
    MindMapTheme(
      name: 'Warm Orange',
      primaryColor: Color(0xFFFF9800),
      secondaryColor: Color(0xFFFFB74D),
      backgroundColor: Color(0xFFFFF3E0),
      connectionColor: Color(0xFFF57C00),
      nodeTextStyle: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
    ),
  ];
}

/// Represents a complete mind map with all its nodes and metadata
class MindMap {
  final String id;
  String title;
  String? description;
  String? sourceFileName;
  Map<String, MindMapNode> nodes;
  String? rootNodeId;
  MindMapLayout layout;
  MindMapTheme theme;
  double zoomLevel;
  Offset panOffset;
  DateTime createdAt;
  DateTime updatedAt;

  MindMap({
    String? id,
    required this.title,
    this.description,
    this.sourceFileName,
    Map<String, MindMapNode>? nodes,
    this.rootNodeId,
    this.layout = MindMapLayout.radial,
    MindMapTheme? theme,
    this.zoomLevel = 1.0,
    this.panOffset = Offset.zero,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        nodes = nodes ?? {},
        theme = theme ?? MindMapTheme.predefinedThemes[0],
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Gets the root node of the mind map
  MindMapNode? get rootNode => rootNodeId != null ? nodes[rootNodeId] : null;

  /// Gets all child nodes of a given node
  List<MindMapNode> getChildNodes(String nodeId) {
    final node = nodes[nodeId];
    if (node == null) return [];
    return node.childrenIds.map((id) => nodes[id]).where((n) => n != null).cast<MindMapNode>().toList();
  }

  /// Gets the parent node of a given node
  MindMapNode? getParentNode(String nodeId) {
    final node = nodes[nodeId];
    if (node?.parentId == null) return null;
    return nodes[node!.parentId];
  }

  /// Adds a new node to the mind map
  void addNode(MindMapNode node, {String? parentId}) {
    if (parentId != null) {
      final parent = nodes[parentId];
      if (parent != null) {
        parent.childrenIds.add(node.id);
        node.parentId = parentId;
        node.level = parent.level + 1;
      }
    }
    
    nodes[node.id] = node;
    
    // Set as root if it's the first node
    rootNodeId ??= node.id;
    
    updatedAt = DateTime.now();
  }

  /// Removes a node and all its children from the mind map
  void removeNode(String nodeId) {
    final node = nodes[nodeId];
    if (node == null) return;

    // Remove from parent's children list
    if (node.parentId != null) {
      final parent = nodes[node.parentId];
      parent?.childrenIds.remove(nodeId);
    }

    // Recursively remove all children
    final childrenToRemove = List.from(node.childrenIds);
    for (final childId in childrenToRemove) {
      removeNode(childId);
    }

    // Remove the node itself
    nodes.remove(nodeId);

    // Update root if necessary
    if (rootNodeId == nodeId) {
      rootNodeId = nodes.isNotEmpty ? nodes.keys.first : null;
    }

    updatedAt = DateTime.now();
  }

  /// Updates an existing node
  void updateNode(String nodeId, MindMapNode updatedNode) {
    if (nodes.containsKey(nodeId)) {
      nodes[nodeId] = updatedNode;
      updatedAt = DateTime.now();
    }
  }

  /// Gets all nodes at a specific level
  List<MindMapNode> getNodesAtLevel(int level) {
    return nodes.values.where((node) => node.level == level).toList();
  }

  /// Gets the maximum depth of the mind map
  int get maxDepth {
    if (nodes.isEmpty) return 0;
    return nodes.values.map((node) => node.level).reduce((a, b) => a > b ? a : b) + 1;
  }

  /// Creates a copy of this mind map
  MindMap copyWith({
    String? title,
    String? description,
    String? sourceFileName,
    MindMapLayout? layout,
    MindMapTheme? theme,
    double? zoomLevel,
    Offset? panOffset,
  }) {
    return MindMap(
      id: id,
      title: title ?? this.title,
      description: description ?? this.description,
      sourceFileName: sourceFileName ?? this.sourceFileName,
      nodes: Map.from(nodes),
      rootNodeId: rootNodeId,
      layout: layout ?? this.layout,
      theme: theme ?? this.theme,
      zoomLevel: zoomLevel ?? this.zoomLevel,
      panOffset: panOffset ?? this.panOffset,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  /// Converts the mind map to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'sourceFileName': sourceFileName,
      'nodes': nodes.map((key, value) => MapEntry(key, value.toJson())),
      'rootNodeId': rootNodeId,
      'layout': layout.name,
      'theme': theme.name,
      'zoomLevel': zoomLevel,
      'panOffset': {'x': panOffset.dx, 'y': panOffset.dy},
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Creates a mind map from JSON
  factory MindMap.fromJson(Map<String, dynamic> json) {
    final nodesMap = <String, MindMapNode>{};
    if (json['nodes'] != null) {
      (json['nodes'] as Map<String, dynamic>).forEach((key, value) {
        nodesMap[key] = MindMapNode.fromJson(value);
      });
    }

    return MindMap(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      sourceFileName: json['sourceFileName'],
      nodes: nodesMap,
      rootNodeId: json['rootNodeId'],
      layout: MindMapLayout.values.firstWhere(
        (e) => e.name == json['layout'],
        orElse: () => MindMapLayout.radial,
      ),
      theme: MindMapTheme.predefinedThemes.firstWhere(
        (t) => t.name == json['theme'],
        orElse: () => MindMapTheme.predefinedThemes[0],
      ),
      zoomLevel: json['zoomLevel']?.toDouble() ?? 1.0,
      panOffset: json['panOffset'] != null
          ? Offset(json['panOffset']['x'], json['panOffset']['y'])
          : Offset.zero,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}
