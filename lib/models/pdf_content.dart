/// Represents extracted content from a PDF document
class PDFContent {
  final String fileName;
  final String fullText;
  final List<PDFSection> sections;
  final Map<String, dynamic> metadata;
  final DateTime extractedAt;

  PDFContent({
    required this.fileName,
    required this.fullText,
    required this.sections,
    this.metadata = const {},
    DateTime? extractedAt,
  }) : extractedAt = extractedAt ?? DateTime.now();

  /// Gets all headings from the PDF content
  List<String> get headings {
    return sections
        .where((section) => section.isHeading)
        .map((section) => section.text.trim())
        .where((text) => text.isNotEmpty)
        .toList();
  }

  /// Gets the main content paragraphs
  List<String> get paragraphs {
    return sections
        .where((section) => !section.isHeading && section.text.trim().isNotEmpty)
        .map((section) => section.text.trim())
        .toList();
  }

  /// Creates a hierarchical structure from the content
  List<ContentHierarchy> get hierarchy {
    final List<ContentHierarchy> result = [];
    ContentHierarchy? currentH1;
    ContentHierarchy? currentH2;
    ContentHierarchy? currentH3;

    for (final section in sections) {
      if (section.isHeading) {
        final hierarchy = ContentHierarchy(
          title: section.text.trim(),
          level: section.headingLevel,
          content: [],
          children: [],
        );

        switch (section.headingLevel) {
          case 1:
            result.add(hierarchy);
            currentH1 = hierarchy;
            currentH2 = null;
            currentH3 = null;
            break;
          case 2:
            if (currentH1 != null) {
              currentH1.children.add(hierarchy);
            } else {
              result.add(hierarchy);
            }
            currentH2 = hierarchy;
            currentH3 = null;
            break;
          case 3:
            if (currentH2 != null) {
              currentH2.children.add(hierarchy);
            } else if (currentH1 != null) {
              currentH1.children.add(hierarchy);
            } else {
              result.add(hierarchy);
            }
            currentH3 = hierarchy;
            break;
          default:
            if (currentH3 != null) {
              currentH3.children.add(hierarchy);
            } else if (currentH2 != null) {
              currentH2.children.add(hierarchy);
            } else if (currentH1 != null) {
              currentH1.children.add(hierarchy);
            } else {
              result.add(hierarchy);
            }
        }
      } else if (section.text.trim().isNotEmpty) {
        // Add content to the most recent heading
        if (currentH3 != null) {
          currentH3.content.add(section.text.trim());
        } else if (currentH2 != null) {
          currentH2.content.add(section.text.trim());
        } else if (currentH1 != null) {
          currentH1.content.add(section.text.trim());
        } else {
          // Create a default section for content without headings
          if (result.isEmpty || result.last.title != 'Content') {
            result.add(ContentHierarchy(
              title: 'Content',
              level: 1,
              content: [],
              children: [],
            ));
          }
          result.last.content.add(section.text.trim());
        }
      }
    }

    return result;
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'fileName': fileName,
      'fullText': fullText,
      'sections': sections.map((s) => s.toJson()).toList(),
      'metadata': metadata,
      'extractedAt': extractedAt.toIso8601String(),
    };
  }

  /// Creates from JSON
  factory PDFContent.fromJson(Map<String, dynamic> json) {
    return PDFContent(
      fileName: json['fileName'],
      fullText: json['fullText'],
      sections: (json['sections'] as List)
          .map((s) => PDFSection.fromJson(s))
          .toList(),
      metadata: json['metadata'] ?? {},
      extractedAt: DateTime.parse(json['extractedAt']),
    );
  }
}

/// Represents a section of content from the PDF
class PDFSection {
  final String text;
  final bool isHeading;
  final int headingLevel;
  final int pageNumber;
  final Map<String, dynamic> formatting;

  PDFSection({
    required this.text,
    this.isHeading = false,
    this.headingLevel = 0,
    this.pageNumber = 1,
    this.formatting = const {},
  });

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'isHeading': isHeading,
      'headingLevel': headingLevel,
      'pageNumber': pageNumber,
      'formatting': formatting,
    };
  }

  /// Creates from JSON
  factory PDFSection.fromJson(Map<String, dynamic> json) {
    return PDFSection(
      text: json['text'],
      isHeading: json['isHeading'] ?? false,
      headingLevel: json['headingLevel'] ?? 0,
      pageNumber: json['pageNumber'] ?? 1,
      formatting: json['formatting'] ?? {},
    );
  }
}

/// Represents hierarchical content structure
class ContentHierarchy {
  final String title;
  final int level;
  final List<String> content;
  final List<ContentHierarchy> children;

  ContentHierarchy({
    required this.title,
    required this.level,
    required this.content,
    required this.children,
  });

  /// Gets all text content including children
  String get allContent {
    final buffer = StringBuffer();
    if (content.isNotEmpty) {
      buffer.writeln(content.join('\n\n'));
    }
    for (final child in children) {
      if (buffer.isNotEmpty) buffer.writeln();
      buffer.write(child.allContent);
    }
    return buffer.toString();
  }

  /// Gets a summary of the content (first 100 characters)
  String get summary {
    final text = allContent.trim();
    if (text.length <= 100) return text;
    return '${text.substring(0, 97)}...';
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'level': level,
      'content': content,
      'children': children.map((c) => c.toJson()).toList(),
    };
  }

  /// Creates from JSON
  factory ContentHierarchy.fromJson(Map<String, dynamic> json) {
    return ContentHierarchy(
      title: json['title'],
      level: json['level'],
      content: List<String>.from(json['content']),
      children: (json['children'] as List)
          .map((c) => ContentHierarchy.fromJson(c))
          .toList(),
    );
  }
}
