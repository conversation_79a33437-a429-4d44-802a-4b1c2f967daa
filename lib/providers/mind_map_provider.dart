import 'dart:io';
import 'package:flutter/material.dart';
import '../models/mind_map.dart';
import '../models/mind_map_node.dart';
import '../services/pdf_parser_service.dart';
import '../services/mind_map_converter.dart';
import '../services/file_manager_service.dart';

/// Provider for managing mind map state and operations
class MindMapProvider extends ChangeNotifier {
  MindMap? _currentMindMap;
  bool _isLoading = false;
  String? _error;
  List<MindMap> _savedMindMaps = [];
  String? _selectedNodeId;
  bool _isEditMode = false;

  // Getters
  MindMap? get currentMindMap => _currentMindMap;
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<MindMap> get savedMindMaps => _savedMindMaps;
  String? get selectedNodeId => _selectedNodeId;
  bool get isEditMode => _isEditMode;
  bool get hasMindMap => _currentMindMap != null;

  MindMapNode? get selectedNode => 
      _selectedNodeId != null ? _currentMindMap?.nodes[_selectedNodeId] : null;

  /// Sets loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Sets error state
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// Creates a new mind map from PDF file
  Future<void> createMindMapFromPDF(
    File pdfFile, {
    MindMapLayout layout = MindMapLayout.radial,
    MindMapTheme? theme,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      // Validate PDF file
      if (!await PDFParserService.isValidPDF(pdfFile)) {
        throw Exception('Invalid PDF file');
      }

      // Parse PDF content
      final pdfContent = await PDFParserService.parsePDF(pdfFile);

      // Convert to mind map
      final mindMap = MindMapConverter.convertToMindMap(
        pdfContent,
        layout: layout,
        theme: theme,
      );

      _currentMindMap = mindMap;
      _selectedNodeId = null;
      _isEditMode = false;

      notifyListeners();
    } catch (e) {
      _setError('Failed to create mind map: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Loads an existing mind map
  Future<void> loadMindMap(String mindMapId) async {
    try {
      _setLoading(true);
      _setError(null);

      final mindMap = await FileManagerService.loadMindMap(mindMapId);
      _currentMindMap = mindMap;
      _selectedNodeId = null;
      _isEditMode = false;

      notifyListeners();
    } catch (e) {
      _setError('Failed to load mind map: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Saves the current mind map
  Future<void> saveMindMap() async {
    if (_currentMindMap == null) return;

    try {
      _setLoading(true);
      _setError(null);

      await FileManagerService.saveMindMap(_currentMindMap!);
      await loadSavedMindMaps();

      notifyListeners();
    } catch (e) {
      _setError('Failed to save mind map: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Loads all saved mind maps
  Future<void> loadSavedMindMaps() async {
    try {
      _savedMindMaps = await FileManagerService.getAllMindMaps();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load saved mind maps: ${e.toString()}');
    }
  }

  /// Deletes a saved mind map
  Future<void> deleteMindMap(String mindMapId) async {
    try {
      await FileManagerService.deleteMindMap(mindMapId);
      await loadSavedMindMaps();
      
      // Clear current mind map if it was deleted
      if (_currentMindMap?.id == mindMapId) {
        _currentMindMap = null;
        _selectedNodeId = null;
        _isEditMode = false;
      }

      notifyListeners();
    } catch (e) {
      _setError('Failed to delete mind map: ${e.toString()}');
    }
  }

  /// Creates a new empty mind map
  void createNewMindMap(String title) {
    final rootNode = MindMapNode(
      title: title,
      subtitle: 'Main Topic',
      color: Colors.blue,
      icon: Icons.account_tree,
      position: const Offset(0, 0),
      size: const Size(160, 80),
      level: 0,
    );

    _currentMindMap = MindMap(title: title);
    _currentMindMap!.addNode(rootNode);
    _selectedNodeId = null;
    _isEditMode = false;

    notifyListeners();
  }

  /// Selects a node
  void selectNode(String? nodeId) {
    _selectedNodeId = nodeId;
    notifyListeners();
  }

  /// Toggles edit mode
  void toggleEditMode() {
    _isEditMode = !_isEditMode;
    if (!_isEditMode) {
      _selectedNodeId = null;
    }
    notifyListeners();
  }

  /// Adds a new node to the mind map
  void addNode(MindMapNode node, {String? parentId}) {
    if (_currentMindMap == null) return;

    _currentMindMap!.addNode(node, parentId: parentId);
    notifyListeners();
  }

  /// Updates an existing node
  void updateNode(String nodeId, MindMapNode updatedNode) {
    if (_currentMindMap == null) return;

    _currentMindMap!.updateNode(nodeId, updatedNode);
    notifyListeners();
  }

  /// Removes a node from the mind map
  void removeNode(String nodeId) {
    if (_currentMindMap == null) return;

    _currentMindMap!.removeNode(nodeId);
    
    // Clear selection if the selected node was removed
    if (_selectedNodeId == nodeId) {
      _selectedNodeId = null;
    }

    notifyListeners();
  }

  /// Updates mind map layout
  void updateLayout(MindMapLayout layout) {
    if (_currentMindMap == null) return;

    _currentMindMap = _currentMindMap!.copyWith(layout: layout);
    
    // Reposition nodes for new layout
    MindMapConverter.positionNodes(_currentMindMap!);
    
    notifyListeners();
  }

  /// Updates mind map theme
  void updateTheme(MindMapTheme theme) {
    if (_currentMindMap == null) return;

    _currentMindMap = _currentMindMap!.copyWith(theme: theme);
    notifyListeners();
  }

  /// Updates zoom level
  void updateZoom(double zoomLevel) {
    if (_currentMindMap == null) return;

    _currentMindMap = _currentMindMap!.copyWith(zoomLevel: zoomLevel.clamp(0.1, 3.0));
    notifyListeners();
  }

  /// Updates pan offset
  void updatePan(Offset panOffset) {
    if (_currentMindMap == null) return;

    _currentMindMap = _currentMindMap!.copyWith(panOffset: panOffset);
    notifyListeners();
  }

  /// Resets zoom and pan
  void resetView() {
    if (_currentMindMap == null) return;

    _currentMindMap = _currentMindMap!.copyWith(
      zoomLevel: 1.0,
      panOffset: Offset.zero,
    );
    notifyListeners();
  }

  /// Exports mind map as image
  Future<File?> exportAsImage() async {
    if (_currentMindMap == null) return null;

    try {
      _setLoading(true);
      _setError(null);

      final file = await FileManagerService.exportMindMapAsImage(_currentMindMap!);
      return file;
    } catch (e) {
      _setError('Failed to export mind map: ${e.toString()}');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Shares mind map
  Future<void> shareMindMap() async {
    if (_currentMindMap == null) return;

    try {
      await FileManagerService.shareMindMap(_currentMindMap!);
    } catch (e) {
      _setError('Failed to share mind map: ${e.toString()}');
    }
  }

  /// Clears current mind map
  void clearMindMap() {
    _currentMindMap = null;
    _selectedNodeId = null;
    _isEditMode = false;
    _error = null;
    notifyListeners();
  }

  /// Clears error
  void clearError() {
    _error = null;
    notifyListeners();
  }


}
