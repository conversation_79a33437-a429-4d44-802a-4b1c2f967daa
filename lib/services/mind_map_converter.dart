import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/pdf_content.dart';
import '../models/mind_map.dart';
import '../models/mind_map_node.dart';

/// Service for converting PDF content into mind map structures
class MindMapConverter {
  static const List<Color> _levelColors = [
    Color(0xFF2196F3), // Blue
    Color(0xFF4CAF50), // Green
    Color(0xFFFF9800), // Orange
    Color(0xFF9C27B0), // Purple
    Color(0xFFF44336), // Red
    Color(0xFF00BCD4), // Cyan
    Color(0xFF795548), // Brown
    Color(0xFF607D8B), // Blue Grey
  ];

  static const List<IconData> _categoryIcons = [
    Icons.article,
    Icons.lightbulb,
    Icons.analytics,
    Icons.psychology,
    Icons.science,
    Icons.business,
    Icons.school,
    Icons.library_books,
  ];

  /// Converts PDF content to a mind map
  static MindMap convertToMindMap(PDFContent pdfContent, {
    MindMapLayout layout = MindMapLayout.radial,
    MindMapTheme? theme,
  }) {
    final mindMap = MindMap(
      title: _generateTitle(pdfContent),
      description: 'Generated from ${pdfContent.fileName}',
      sourceFileName: pdfContent.fileName,
      layout: layout,
      theme: theme ?? MindMapTheme.predefinedThemes[0],
    );

    // Create root node
    final rootNode = MindMapNode(
      title: mindMap.title,
      subtitle: 'PDF Mind Map',
      description: 'Main topic extracted from ${pdfContent.fileName}',
      color: _levelColors[0],
      icon: Icons.account_tree,
      position: const Offset(0, 0),
      size: const Size(160, 80),
      level: 0,
    );

    mindMap.addNode(rootNode);

    // Convert hierarchical content to nodes
    final hierarchy = pdfContent.hierarchy;
    if (hierarchy.isNotEmpty && hierarchy.length > 1) {
      _convertHierarchyToNodes(mindMap, hierarchy, rootNode.id);
    } else {
      // Fallback: create nodes from headings and paragraphs
      _createFallbackStructure(mindMap, pdfContent, rootNode.id);
    }

    // Ensure we have at least some nodes
    if (mindMap.nodes.length <= 1) {
      debugPrint('Creating default nodes because only ${mindMap.nodes.length} nodes exist');
      _createDefaultNodes(mindMap, rootNode.id);
    }

    debugPrint('Final mind map has ${mindMap.nodes.length} nodes');

    // Position nodes based on layout
    positionNodes(mindMap);

    return mindMap;
  }

  /// Converts hierarchical content to mind map nodes
  static void _convertHierarchyToNodes(
    MindMap mindMap,
    List<ContentHierarchy> hierarchy,
    String parentId,
  ) {
    for (int i = 0; i < hierarchy.length; i++) {
      final item = hierarchy[i];
      final node = _createNodeFromHierarchy(item, i, parentId);
      mindMap.addNode(node, parentId: parentId);

      // Recursively add children
      if (item.children.isNotEmpty) {
        _convertHierarchyToNodes(mindMap, item.children, node.id);
      }
    }
  }

  /// Creates a node from hierarchical content
  static MindMapNode _createNodeFromHierarchy(
    ContentHierarchy hierarchy,
    int index,
    String parentId,
  ) {
    final level = hierarchy.level;
    final color = _levelColors[level % _levelColors.length];
    final icon = _categoryIcons[index % _categoryIcons.length];

    // Determine node size based on content
    final hasContent = hierarchy.content.isNotEmpty || hierarchy.children.isNotEmpty;
    final size = hasContent ? const Size(140, 70) : const Size(120, 60);

    return MindMapNode(
      title: _truncateText(hierarchy.title, 30),
      subtitle: hierarchy.content.isNotEmpty ? _truncateText(hierarchy.content.first, 50) : null,
      description: hierarchy.summary,
      color: color,
      icon: icon,
      size: size,
      level: level,
    );
  }

  /// Creates fallback structure when hierarchy is not available
  static void _createFallbackStructure(
    MindMap mindMap,
    PDFContent pdfContent,
    String rootId,
  ) {
    final headings = pdfContent.headings;
    final paragraphs = pdfContent.paragraphs;

    // Create nodes from headings
    if (headings.isNotEmpty) {
      for (int i = 0; i < headings.length && i < 8; i++) {
        final heading = headings[i];
        final node = MindMapNode(
          title: _truncateText(heading, 30),
          subtitle: 'Section ${i + 1}',
          description: _findRelatedContent(heading, paragraphs),
          color: _levelColors[(i + 1) % _levelColors.length],
          icon: _categoryIcons[i % _categoryIcons.length],
          level: 1,
        );
        mindMap.addNode(node, parentId: rootId);
      }
    } else {
      // Create nodes from paragraphs if no headings found
      _createNodesFromParagraphs(mindMap, paragraphs, rootId);
    }
  }

  /// Creates default nodes when no content is available
  static void _createDefaultNodes(MindMap mindMap, String rootId) {
    final defaultTopics = [
      'المقدمة',
      'النقاط الرئيسية',
      'التفاصيل',
      'الخلاصة',
    ];

    for (int i = 0; i < defaultTopics.length; i++) {
      final node = MindMapNode(
        title: defaultTopics[i],
        subtitle: 'قسم ${i + 1}',
        description: 'محتوى مستخرج من المستند',
        color: _levelColors[(i + 1) % _levelColors.length],
        icon: _categoryIcons[i % _categoryIcons.length],
        level: 1,
      );
      mindMap.addNode(node, parentId: rootId);
    }
  }

  /// Creates nodes from paragraphs when no headings are available
  static void _createNodesFromParagraphs(
    MindMap mindMap,
    List<String> paragraphs,
    String rootId,
  ) {
    final maxNodes = 6;
    final step = (paragraphs.length / maxNodes).ceil();

    for (int i = 0; i < paragraphs.length && i < maxNodes * step; i += step) {
      final paragraph = paragraphs[i];
      final title = _extractTitleFromParagraph(paragraph);
      
      final node = MindMapNode(
        title: title,
        subtitle: 'Content ${(i ~/ step) + 1}',
        description: _truncateText(paragraph, 200),
        color: _levelColors[(i ~/ step + 1) % _levelColors.length],
        icon: _categoryIcons[(i ~/ step) % _categoryIcons.length],
        level: 1,
      );
      mindMap.addNode(node, parentId: rootId);
    }
  }

  /// Positions nodes based on the mind map layout
  static void positionNodes(MindMap mindMap) {
    switch (mindMap.layout) {
      case MindMapLayout.radial:
        _positionRadialLayout(mindMap);
        break;
      case MindMapLayout.tree:
        _positionTreeLayout(mindMap);
        break;
      case MindMapLayout.organizational:
        _positionOrganizationalLayout(mindMap);
        break;
      case MindMapLayout.flowchart:
        _positionFlowchartLayout(mindMap);
        break;
    }
  }

  /// Positions nodes in radial layout
  static void _positionRadialLayout(MindMap mindMap) {
    final rootNode = mindMap.rootNode;
    if (rootNode == null) return;

    // Position root at center
    final updatedRoot = rootNode.copyWith(position: const Offset(0, 0));
    mindMap.updateNode(rootNode.id, updatedRoot);

    final children = mindMap.getChildNodes(rootNode.id);
    if (children.isEmpty) return;

    const double radius = 200;
    const double centerX = 0;
    const double centerY = 0;

    debugPrint('Positioning ${children.length} children in radial layout');

    for (int i = 0; i < children.length; i++) {
      final angle = (2 * pi * i) / children.length;
      final x = centerX + radius * cos(angle);
      final y = centerY + radius * sin(angle);

      debugPrint('Child $i: ${children[i].title} at ($x, $y)');

      final updatedChild = children[i].copyWith(position: Offset(x, y));
      mindMap.updateNode(children[i].id, updatedChild);

      // Position grandchildren
      _positionRadialChildren(mindMap, children[i].id, Offset(x, y), radius * 0.6, angle);
    }
  }

  /// Positions children in radial layout recursively
  static void _positionRadialChildren(
    MindMap mindMap,
    String parentId,
    Offset parentPosition,
    double radius,
    double baseAngle,
  ) {
    final children = mindMap.getChildNodes(parentId);
    if (children.isEmpty) return;

    const double angleSpread = pi / 3; // 60 degrees spread
    final double startAngle = baseAngle - angleSpread / 2;

    for (int i = 0; i < children.length; i++) {
      final angle = startAngle + (angleSpread * i) / max(1, children.length - 1);
      final x = parentPosition.dx + radius * cos(angle);
      final y = parentPosition.dy + radius * sin(angle);

      final updatedChild = children[i].copyWith(position: Offset(x, y));
      mindMap.updateNode(children[i].id, updatedChild);
    }
  }

  /// Positions nodes in tree layout
  static void _positionTreeLayout(MindMap mindMap) {
    final rootNode = mindMap.rootNode;
    if (rootNode == null) return;

    const double levelHeight = 150;
    const double nodeSpacing = 180;

    _positionTreeLevel(mindMap, [rootNode.id], 0, 0, levelHeight, nodeSpacing);
  }

  /// Positions a level of nodes in tree layout
  static void _positionTreeLevel(
    MindMap mindMap,
    List<String> nodeIds,
    int level,
    double centerX,
    double levelHeight,
    double nodeSpacing,
  ) {
    if (nodeIds.isEmpty) return;

    final double totalWidth = (nodeIds.length - 1) * nodeSpacing;
    final double startX = centerX - totalWidth / 2;
    final double y = level * levelHeight;

    final List<String> nextLevelNodes = [];

    for (int i = 0; i < nodeIds.length; i++) {
      final nodeId = nodeIds[i];
      final node = mindMap.nodes[nodeId];
      if (node == null) continue;

      final x = startX + i * nodeSpacing;
      final updatedNode = node.copyWith(position: Offset(x, y));
      mindMap.updateNode(nodeId, updatedNode);

      // Collect children for next level
      nextLevelNodes.addAll(mindMap.getChildNodes(nodeId).map((n) => n.id));
    }

    // Position next level
    if (nextLevelNodes.isNotEmpty) {
      _positionTreeLevel(mindMap, nextLevelNodes, level + 1, centerX, levelHeight, nodeSpacing);
    }
  }

  /// Positions nodes in organizational layout
  static void _positionOrganizationalLayout(MindMap mindMap) {
    // Similar to tree but with different spacing
    _positionTreeLayout(mindMap);
  }

  /// Positions nodes in flowchart layout
  static void _positionFlowchartLayout(MindMap mindMap) {
    final rootNode = mindMap.rootNode;
    if (rootNode == null) return;

    const double stepX = 200;
    const double stepY = 120;
    double currentX = 0;
    double currentY = 0;

    final allNodes = [rootNode.id];
    _collectAllNodes(mindMap, rootNode.id, allNodes);

    for (int i = 0; i < allNodes.length; i++) {
      final nodeId = allNodes[i];
      final node = mindMap.nodes[nodeId];
      if (node == null) continue;

      final updatedNode = node.copyWith(position: Offset(currentX, currentY));
      mindMap.updateNode(nodeId, updatedNode);

      currentX += stepX;
      if ((i + 1) % 3 == 0) {
        currentX = 0;
        currentY += stepY;
      }
    }
  }

  /// Collects all node IDs recursively
  static void _collectAllNodes(MindMap mindMap, String nodeId, List<String> result) {
    final children = mindMap.getChildNodes(nodeId);
    for (final child in children) {
      if (!result.contains(child.id)) {
        result.add(child.id);
        _collectAllNodes(mindMap, child.id, result);
      }
    }
  }

  /// Generates a title for the mind map
  static String _generateTitle(PDFContent pdfContent) {
    // Try to use PDF metadata title
    if (pdfContent.metadata['title'] != null && 
        pdfContent.metadata['title'].toString().trim().isNotEmpty) {
      return pdfContent.metadata['title'].toString().trim();
    }

    // Try to extract title from first heading
    final headings = pdfContent.headings;
    if (headings.isNotEmpty) {
      return _truncateText(headings.first, 50);
    }

    // Use filename as fallback
    final fileName = pdfContent.fileName;
    final nameWithoutExtension = fileName.replaceAll(RegExp(r'\.[^.]+$'), '');
    return nameWithoutExtension.replaceAll(RegExp(r'[_-]'), ' ');
  }

  /// Finds content related to a heading
  static String _findRelatedContent(String heading, List<String> paragraphs) {
    // Simple heuristic: find the first paragraph that might be related
    for (final paragraph in paragraphs) {
      if (paragraph.length > 50 && paragraph.length < 300) {
        return _truncateText(paragraph, 150);
      }
    }
    return 'Content related to $heading';
  }

  /// Extracts a title from a paragraph
  static String _extractTitleFromParagraph(String paragraph) {
    final sentences = paragraph.split('.');
    if (sentences.isNotEmpty) {
      final firstSentence = sentences.first.trim();
      if (firstSentence.length <= 50) {
        return firstSentence;
      }
    }
    
    final words = paragraph.split(' ');
    if (words.length >= 3) {
      return words.take(5).join(' ');
    }
    
    return _truncateText(paragraph, 30);
  }

  /// Truncates text to specified length
  static String _truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength - 3)}...';
  }
}
