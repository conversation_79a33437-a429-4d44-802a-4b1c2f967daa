import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../models/mind_map.dart';

/// Service for managing file operations and storage
class FileManagerService {
  static const String _mindMapsFolder = 'mind_maps';
  static const String _exportsFolder = 'exports';

  /// Gets the application documents directory
  static Future<Directory> _getAppDirectory() async {
    final directory = await getApplicationDocumentsDirectory();
    return directory;
  }

  /// Gets the mind maps storage directory
  static Future<Directory> _getMindMapsDirectory() async {
    final appDir = await _getAppDirectory();
    final mindMapsDir = Directory('${appDir.path}/$_mindMapsFolder');
    
    if (!await mindMapsDir.exists()) {
      await mindMapsDir.create(recursive: true);
    }
    
    return mindMapsDir;
  }

  /// Gets the exports storage directory
  static Future<Directory> _getExportsDirectory() async {
    final appDir = await _getAppDirectory();
    final exportsDir = Directory('${appDir.path}/$_exportsFolder');
    
    if (!await exportsDir.exists()) {
      await exportsDir.create(recursive: true);
    }
    
    return exportsDir;
  }

  /// Saves a mind map to storage
  static Future<void> saveMindMap(MindMap mindMap) async {
    try {
      final directory = await _getMindMapsDirectory();
      final file = File('${directory.path}/${mindMap.id}.json');
      
      final jsonData = json.encode(mindMap.toJson());
      await file.writeAsString(jsonData);
    } catch (e) {
      throw Exception('Failed to save mind map: ${e.toString()}');
    }
  }

  /// Loads a mind map from storage
  static Future<MindMap> loadMindMap(String mindMapId) async {
    try {
      final directory = await _getMindMapsDirectory();
      final file = File('${directory.path}/$mindMapId.json');
      
      if (!await file.exists()) {
        throw Exception('Mind map not found');
      }
      
      final jsonString = await file.readAsString();
      final jsonData = json.decode(jsonString);
      
      return MindMap.fromJson(jsonData);
    } catch (e) {
      throw Exception('Failed to load mind map: ${e.toString()}');
    }
  }

  /// Gets all saved mind maps
  static Future<List<MindMap>> getAllMindMaps() async {
    try {
      final directory = await _getMindMapsDirectory();
      final files = await directory.list().toList();
      
      final mindMaps = <MindMap>[];
      
      for (final file in files) {
        if (file is File && file.path.endsWith('.json')) {
          try {
            final jsonString = await file.readAsString();
            final jsonData = json.decode(jsonString);
            final mindMap = MindMap.fromJson(jsonData);
            mindMaps.add(mindMap);
          } catch (e) {
            // Skip corrupted files
            // print('Skipping corrupted file: ${file.path}');
          }
        }
      }
      
      // Sort by last updated
      mindMaps.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
      
      return mindMaps;
    } catch (e) {
      throw Exception('Failed to load mind maps: ${e.toString()}');
    }
  }

  /// Deletes a mind map from storage
  static Future<void> deleteMindMap(String mindMapId) async {
    try {
      final directory = await _getMindMapsDirectory();
      final file = File('${directory.path}/$mindMapId.json');
      
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      throw Exception('Failed to delete mind map: ${e.toString()}');
    }
  }

  /// Exports mind map as image
  static Future<File> exportMindMapAsImage(MindMap mindMap) async {
    try {
      final directory = await _getExportsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${mindMap.title.replaceAll(RegExp(r'[^\w\s-]'), '')}_$timestamp.png';
      final file = File('${directory.path}/$fileName');
      
      // This is a placeholder - in a real implementation, you would
      // capture the actual mind map widget as an image
      final imageBytes = await _generateMindMapImage(mindMap);
      await file.writeAsBytes(imageBytes);
      
      return file;
    } catch (e) {
      throw Exception('Failed to export mind map: ${e.toString()}');
    }
  }

  /// Generates a placeholder image for the mind map
  static Future<Uint8List> _generateMindMapImage(MindMap mindMap) async {
    // This is a placeholder implementation
    // In a real app, you would use the Screenshot package to capture
    // the actual mind map widget
    
    // For now, return a simple placeholder
    final bytes = Uint8List(100);
    for (int i = 0; i < bytes.length; i++) {
      bytes[i] = (i * 255 / bytes.length).round();
    }
    return bytes;
  }

  /// Shares a mind map
  static Future<void> shareMindMap(MindMap mindMap) async {
    try {
      // Export as image first
      final imageFile = await exportMindMapAsImage(mindMap);
      
      // Share the image
      await Share.shareXFiles(
        [XFile(imageFile.path)],
        text: 'Mind Map: ${mindMap.title}',
        subject: 'Shared Mind Map',
      );
    } catch (e) {
      throw Exception('Failed to share mind map: ${e.toString()}');
    }
  }

  /// Exports mind map as JSON
  static Future<File> exportMindMapAsJson(MindMap mindMap) async {
    try {
      final directory = await _getExportsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${mindMap.title.replaceAll(RegExp(r'[^\w\s-]'), '')}_$timestamp.json';
      final file = File('${directory.path}/$fileName');
      
      final jsonData = json.encode(mindMap.toJson());
      await file.writeAsString(jsonData);
      
      return file;
    } catch (e) {
      throw Exception('Failed to export mind map as JSON: ${e.toString()}');
    }
  }

  /// Imports mind map from JSON file
  static Future<MindMap> importMindMapFromJson(File jsonFile) async {
    try {
      final jsonString = await jsonFile.readAsString();
      final jsonData = json.decode(jsonString);
      
      return MindMap.fromJson(jsonData);
    } catch (e) {
      throw Exception('Failed to import mind map: ${e.toString()}');
    }
  }

  /// Gets storage usage information
  static Future<Map<String, dynamic>> getStorageInfo() async {
    try {
      final mindMapsDir = await _getMindMapsDirectory();
      final exportsDir = await _getExportsDirectory();
      
      int mindMapsSize = 0;
      int exportsSize = 0;
      int mindMapsCount = 0;
      int exportsCount = 0;
      
      // Calculate mind maps storage
      final mindMapFiles = await mindMapsDir.list().toList();
      for (final file in mindMapFiles) {
        if (file is File) {
          mindMapsSize += await file.length();
          mindMapsCount++;
        }
      }
      
      // Calculate exports storage
      final exportFiles = await exportsDir.list().toList();
      for (final file in exportFiles) {
        if (file is File) {
          exportsSize += await file.length();
          exportsCount++;
        }
      }
      
      return {
        'mindMapsSize': mindMapsSize,
        'exportsSize': exportsSize,
        'totalSize': mindMapsSize + exportsSize,
        'mindMapsCount': mindMapsCount,
        'exportsCount': exportsCount,
      };
    } catch (e) {
      throw Exception('Failed to get storage info: ${e.toString()}');
    }
  }

  /// Clears all exported files
  static Future<void> clearExports() async {
    try {
      final directory = await _getExportsDirectory();
      final files = await directory.list().toList();
      
      for (final file in files) {
        if (file is File) {
          await file.delete();
        }
      }
    } catch (e) {
      throw Exception('Failed to clear exports: ${e.toString()}');
    }
  }

  /// Creates a backup of all mind maps
  static Future<File> createBackup() async {
    try {
      final mindMaps = await getAllMindMaps();
      final backupData = {
        'version': '1.0',
        'createdAt': DateTime.now().toIso8601String(),
        'mindMaps': mindMaps.map((m) => m.toJson()).toList(),
      };
      
      final directory = await _getExportsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'mind_maps_backup_$timestamp.json';
      final file = File('${directory.path}/$fileName');
      
      final jsonData = json.encode(backupData);
      await file.writeAsString(jsonData);
      
      return file;
    } catch (e) {
      throw Exception('Failed to create backup: ${e.toString()}');
    }
  }

  /// Restores mind maps from backup
  static Future<List<MindMap>> restoreFromBackup(File backupFile) async {
    try {
      final jsonString = await backupFile.readAsString();
      final backupData = json.decode(jsonString);
      
      if (backupData['mindMaps'] == null) {
        throw Exception('Invalid backup file format');
      }
      
      final restoredMindMaps = <MindMap>[];
      
      for (final mindMapData in backupData['mindMaps']) {
        final mindMap = MindMap.fromJson(mindMapData);
        await saveMindMap(mindMap);
        restoredMindMaps.add(mindMap);
      }
      
      return restoredMindMaps;
    } catch (e) {
      throw Exception('Failed to restore from backup: ${e.toString()}');
    }
  }
}
