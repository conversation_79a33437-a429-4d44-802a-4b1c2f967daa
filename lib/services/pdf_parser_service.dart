import 'dart:io';
import 'dart:typed_data';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import '../models/pdf_content.dart';

/// Service for parsing PDF documents and extracting structured content
class PDFParserService {
  static const int _maxFileSize = 50 * 1024 * 1024; // 50MB limit

  /// Parses a PDF file and extracts structured content
  static Future<PDFContent> parsePDF(File file) async {
    try {
      // Check file size
      final fileSize = await file.length();
      if (fileSize > _maxFileSize) {
        throw Exception('File size exceeds 50MB limit');
      }

      // Read file bytes
      final Uint8List bytes = await file.readAsBytes();
      
      // Load PDF document
      final PdfDocument document = PdfDocument(inputBytes: bytes);
      
      final List<PDFSection> sections = [];
      final StringBuffer fullTextBuffer = StringBuffer();
      
      // Extract text from all pages
      final PdfTextExtractor textExtractor = PdfTextExtractor(document);
      final String allText = textExtractor.extractText();

      // Split text by pages (simple approach)
      final pages = allText.split('\n\n\n'); // Assuming page breaks create multiple line breaks

      for (int i = 0; i < pages.length; i++) {
        final String pageText = pages[i];
        
        if (pageText.trim().isNotEmpty) {
          fullTextBuffer.writeln(pageText);
          
          // Process page text into sections
          final pageSections = _processPageText(pageText, i + 1);
          sections.addAll(pageSections);
        }
      }
      
      // Extract metadata
      final metadata = _extractMetadata(document);
      
      // Dispose document
      document.dispose();
      
      return PDFContent(
        fileName: file.path.split('/').last,
        fullText: fullTextBuffer.toString(),
        sections: sections,
        metadata: metadata,
      );
      
    } catch (e) {
      throw Exception('Failed to parse PDF: ${e.toString()}');
    }
  }

  /// Processes page text into structured sections
  static List<PDFSection> _processPageText(String pageText, int pageNumber) {
    final List<PDFSection> sections = [];
    final lines = pageText.split('\n');
    
    for (final line in lines) {
      final trimmedLine = line.trim();
      if (trimmedLine.isEmpty) continue;
      
      // Detect headings based on various criteria
      final headingInfo = _detectHeading(trimmedLine);
      
      sections.add(PDFSection(
        text: trimmedLine,
        isHeading: headingInfo['isHeading'],
        headingLevel: headingInfo['level'],
        pageNumber: pageNumber,
        formatting: headingInfo['formatting'],
      ));
    }
    
    return sections;
  }

  /// Detects if a line is a heading and determines its level
  static Map<String, dynamic> _detectHeading(String line) {
    final Map<String, dynamic> result = {
      'isHeading': false,
      'level': 0,
      'formatting': <String, dynamic>{},
    };

    // Skip very long lines (likely paragraphs)
    if (line.length > 100) {
      return result;
    }

    // Check for numbered headings (1., 1.1, 1.1.1, etc.)
    final numberedHeadingRegex = RegExp(r'^(\d+\.)+\s*(.+)$');
    final numberedMatch = numberedHeadingRegex.firstMatch(line);
    if (numberedMatch != null) {
      final numberPart = numberedMatch.group(1)!;
      final level = numberPart.split('.').length - 1;
      result['isHeading'] = true;
      result['level'] = level.clamp(1, 6);
      result['formatting']['numbered'] = true;
      return result;
    }

    // Check for Roman numeral headings
    final romanRegex = RegExp(r'^[IVX]+\.\s*(.+)$', caseSensitive: false);
    if (romanRegex.hasMatch(line)) {
      result['isHeading'] = true;
      result['level'] = 1;
      result['formatting']['roman'] = true;
      return result;
    }

    // Check for alphabetic headings (A., B., etc.)
    final alphaRegex = RegExp(r'^[A-Z]\.\s*(.+)$');
    if (alphaRegex.hasMatch(line)) {
      result['isHeading'] = true;
      result['level'] = 2;
      result['formatting']['alphabetic'] = true;
      return result;
    }

    // Check for all caps (potential heading)
    if (line == line.toUpperCase() && line.length > 3 && line.length < 50) {
      // Additional checks to avoid false positives
      if (!line.contains('.') || line.endsWith(':')) {
        result['isHeading'] = true;
        result['level'] = _estimateHeadingLevel(line);
        result['formatting']['allCaps'] = true;
        return result;
      }
    }

    // Check for title case with specific patterns
    if (_isTitleCase(line) && line.length < 80) {
      // Check if it ends with colon or has no punctuation
      if (line.endsWith(':') || !_hasSentencePunctuation(line)) {
        result['isHeading'] = true;
        result['level'] = _estimateHeadingLevel(line);
        result['formatting']['titleCase'] = true;
        return result;
      }
    }

    // Check for lines that start with common heading words
    final headingWords = [
      'chapter', 'section', 'introduction', 'conclusion', 'summary',
      'overview', 'background', 'methodology', 'results', 'discussion',
      'abstract', 'preface', 'appendix', 'references', 'bibliography'
    ];
    
    final lowerLine = line.toLowerCase();
    for (final word in headingWords) {
      if (lowerLine.startsWith(word)) {
        result['isHeading'] = true;
        result['level'] = word == 'chapter' ? 1 : 2;
        result['formatting']['keywordBased'] = true;
        return result;
      }
    }

    return result;
  }

  /// Estimates heading level based on line characteristics
  static int _estimateHeadingLevel(String line) {
    if (line.length < 20) return 1;
    if (line.length < 40) return 2;
    return 3;
  }

  /// Checks if text is in title case
  static bool _isTitleCase(String text) {
    final words = text.split(' ');
    if (words.length < 2) return false;
    
    int titleCaseWords = 0;
    for (final word in words) {
      if (word.isNotEmpty && word[0] == word[0].toUpperCase()) {
        titleCaseWords++;
      }
    }
    
    return titleCaseWords >= words.length * 0.7; // At least 70% title case
  }

  /// Checks if text has sentence punctuation
  static bool _hasSentencePunctuation(String text) {
    return text.contains('.') || text.contains('!') || text.contains('?');
  }

  /// Extracts metadata from PDF document
  static Map<String, dynamic> _extractMetadata(PdfDocument document) {
    final metadata = <String, dynamic>{};
    
    try {
      if (document.documentInformation.title.isNotEmpty) {
        metadata['title'] = document.documentInformation.title;
      }
      if (document.documentInformation.author.isNotEmpty) {
        metadata['author'] = document.documentInformation.author;
      }
      if (document.documentInformation.subject.isNotEmpty) {
        metadata['subject'] = document.documentInformation.subject;
      }
      if (document.documentInformation.keywords.isNotEmpty) {
        metadata['keywords'] = document.documentInformation.keywords;
      }
      if (document.documentInformation.creator.isNotEmpty) {
        metadata['creator'] = document.documentInformation.creator;
      }
      if (document.documentInformation.producer.isNotEmpty) {
        metadata['producer'] = document.documentInformation.producer;
      }
      
      metadata['pageCount'] = document.pages.count;
      metadata['creationDate'] = document.documentInformation.creationDate?.toIso8601String();
      metadata['modificationDate'] = document.documentInformation.modificationDate?.toIso8601String();
    } catch (e) {
      // Ignore metadata extraction errors
    }
    
    return metadata;
  }

  /// Validates if a file is a valid PDF
  static Future<bool> isValidPDF(File file) async {
    try {
      final bytes = await file.readAsBytes();
      
      // Check PDF header
      if (bytes.length < 4) return false;
      
      final header = String.fromCharCodes(bytes.take(4));
      return header == '%PDF';
    } catch (e) {
      return false;
    }
  }

  /// Gets basic PDF information without full parsing
  static Future<Map<String, dynamic>> getPDFInfo(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final document = PdfDocument(inputBytes: bytes);
      
      final info = {
        'fileName': file.path.split('/').last,
        'fileSize': await file.length(),
        'pageCount': document.pages.count,
        'title': document.documentInformation.title,
        'author': document.documentInformation.author,
      };
      
      document.dispose();
      return info;
    } catch (e) {
      throw Exception('Failed to get PDF info: ${e.toString()}');
    }
  }
}
