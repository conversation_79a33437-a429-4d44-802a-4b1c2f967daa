# Maper - محول PDF إلى خريطة عقل

تطبيق احترافي لتحويل مستندات PDF إلى خرائط عقل تفاعلية وجذابة بصرياً.

## المميزات الرئيسية

### 🔄 تحويل PDF
- استيراد ملفات PDF وتحليلها تلقائياً
- استخراج النصوص والعناوين والهيكل من المستندات
- تحويل ذكي للمحتوى إلى تنسيق خريطة عقل هرمية
- دعم أنواع مختلفة من ملفات PDF

### 🎨 تصميم احترافي
- واجهة مستخدم حديثة ومتجاوبة
- رسوميات عالية الجودة وحركات سلسة
- أنماط ألوان قابلة للتخصيص
- رسوميات متجهة للعرض الواضح على جميع أحجام الشاشات

### 🗺️ مميزات خريطة العقل
- تخطيطات متعددة (شعاعي، شجري، تنظيمي، مخطط انسيابي)
- تحرير العقد بالسحب والإفلات
- إضافة/تحرير/حذف العقد والاتصالات
- خيارات تنسيق نص غنية للعقد
- دعم الأيقونات والصور للتحسين البصري

### 📱 متطلبات تقنية
- تطوير Flutter أصلي
- أداء محسن للتعامل مع ملفات PDF الكبيرة
- وظائف غير متصلة (لا يتطلب إنترنت للمميزات الأساسية)
- نظام إدارة ملفات لحفظ وتنظيم المشاريع
- تصميم متجاوب للأجهزة اللوحية والهواتف

## كيفية الاستخدام

### 1. استيراد PDF
- اضغط على زر "استيراد PDF" في الشاشة الرئيسية
- اختر ملف PDF من جهازك
- اختر نمط التخطيط والألوان المفضل
- اضغط "إنشاء خريطة عقل"

### 2. تحرير خريطة العقل
- اضغط على زر "تحرير" لتفعيل وضع التحرير
- اضغط على عقدة لتحديدها
- اضغط مرتين على عقدة لتحريرها
- اسحب العقد لإعادة ترتيبها
- استخدم الأزرار العائمة لإضافة أو حذف العقد

### 3. تخصيص المظهر
- اضغط على أيقونة الإعدادات في شريط الأدوات
- اختر من بين تخطيطات مختلفة
- غير نمط الألوان حسب تفضيلك

### 4. حفظ ومشاركة
- احفظ خريطة العقل للوصول إليها لاحقاً
- صدر كصورة أو ملف JSON
- شارك خريطة العقل مع الآخرين

## البناء والتشغيل

### متطلبات النظام
- Flutter SDK 3.7.2 أو أحدث
- Dart 3.0 أو أحدث
- Android Studio أو VS Code
- جهاز Android أو محاكي

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd maper
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
flutter run
```

4. **بناء APK للإنتاج**
```bash
flutter build apk --release
```

## هيكل المشروع

```
lib/
├── main.dart                 # نقطة دخول التطبيق
├── models/                   # نماذج البيانات
│   ├── mind_map.dart
│   ├── mind_map_node.dart
│   └── pdf_content.dart
├── services/                 # خدمات التطبيق
│   ├── pdf_parser_service.dart
│   ├── mind_map_converter.dart
│   └── file_manager_service.dart
├── providers/                # إدارة الحالة
│   └── mind_map_provider.dart
├── screens/                  # شاشات التطبيق
│   ├── home_screen.dart
│   ├── pdf_import_screen.dart
│   ├── mind_map_editor_screen.dart
│   └── settings_screen.dart
├── widgets/                  # مكونات واجهة المستخدم
│   ├── mind_map_canvas.dart
│   ├── mind_map_node_widget.dart
│   ├── mind_map_connection.dart
│   ├── node_editor_dialog.dart
│   ├── mind_map_card.dart
│   ├── loading_overlay.dart
│   └── error_dialog.dart
└── utils/                    # أدوات مساعدة
    └── app_theme.dart
```

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
